package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(SolicitudFirma.class)
public class SolicitudFirma_ { 

    public static volatile SingularAttribute<SolicitudFirma, Long> idSolicitudFirma;
    public static volatile SingularAttribute<SolicitudFirma, String> tipoOrdenFirma;
    public static volatile SingularAttribute<SolicitudFirma, Integer> ordenFirma;
    public static volatile SingularAttribute<SolicitudFirma, Date> fechaRegistro;
    public static volatile SingularAttribute<SolicitudFirma, Date> fechaVencimiento;
    public static volatile SingularAttribute<SolicitudFirma, Long> idUsuario;
    public static volatile SingularAttribute<SolicitudFirma, Date> fechaFirma;
    public static volatile SingularAttribute<SolicitudFirma, String> emailFirmante;
    public static volatile SingularAttribute<SolicitudFirma, String> rolFirmante;
    public static volatile SingularAttribute<SolicitudFirma, Long> idArchivoFirma;
    public static volatile SingularAttribute<SolicitudFirma, Boolean> firmado;

}