package se.firme.ms.models.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.dao.IAdmUsuarioDao;
import se.firme.ms.datos.models.dao.ITipoDocumentoDao;
import se.firme.ms.datos.models.dao.IUsuarioDao;
import se.firme.ms.datos.models.dto.FirmanteOrdenDTO;
import se.firme.ms.datos.models.dto.SolicitudFirmaUnificadaDTO;
import se.firme.ms.datos.models.entity.AdmUsuarioDf;
import se.firme.ms.datos.models.entity.TipoDocumento;
import se.firme.ms.datos.models.entity.Usuario;
import java.util.*;
import java.util.logging.Logger;

@Service
public class UsuarioValidationService {
    
    private static Logger logger = Logger.getLogger(UsuarioValidationService.class.getName());
    
    @Autowired
    private IUsuarioDao usuarioDao;
    
    @Autowired
    private ITipoDocumentoDao tipoDocumentoDao;
    
    @Autowired
    private ServicioService servicioService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private IAdmUsuarioDao admUsuarioDao;

    public Object usuarioService;

    

    /**
     * Verifica automáticamente si un firmante está registrado en el sistema
     * y valida que los datos proporcionados coincidan con los de la BD
     */
    public boolean verificarSiFirmanteEstaRegistrado(FirmanteOrdenDTO firmante) throws FirmaException {
        try {
            logger.info("🔍 Verificando si firmante está registrado: " + firmante.getEmail());
            
            Usuario usuarioEncontrado = null;
            String metodoEncontrado = "";
            
            // 1. Buscar por email usando DAO directamente
            try {
                Optional<Usuario> usuarioPorEmail = usuarioDao.findByEmail(firmante.getEmail());
                if (usuarioPorEmail.isPresent()) {
                    usuarioEncontrado = usuarioPorEmail.get();
                    metodoEncontrado = "EMAIL";
                    logger.info("✅ Usuario encontrado por email: " + firmante.getEmail() + " (ID: " + usuarioEncontrado.getIdUsuario() + ")");
                    
                    // **🎯 VERIFICAR SI ES UN USUARIO RECIÉN CREADO AUTOMÁTICAMENTE**
                    if (esUsuarioRecienCreadoAutomaticamente(usuarioEncontrado)) {
                        logger.info("🔄 Usuario fue creado automáticamente en esta sesión - Tratando como NO REGISTRADO para TyC");
                        return false; // Tratarlo como no registrado para efectos de TyC
                    }
                }
            } catch (Exception e) {
                logger.warning("Error buscando por email: " + e.getMessage());
            }
            
            // 2. Si no se encontró por email y tiene número de documento, buscar por documento
            if (usuarioEncontrado == null && firmante.getNumeroDocumento() != null && !firmante.getNumeroDocumento().trim().isEmpty()) {
                try {
                    Optional<Usuario> usuarioPorDocumento = usuarioDao.findByNumeroDocumento(firmante.getNumeroDocumento());
                    if (usuarioPorDocumento.isPresent()) {
                        usuarioEncontrado = usuarioPorDocumento.get();
                        metodoEncontrado = "DOCUMENTO";
                        logger.info("✅ Usuario encontrado por documento: " + firmante.getNumeroDocumento() + " (ID: " + usuarioEncontrado.getIdUsuario() + ")");
                        
                        // **VERIFICAR SI ES UN USUARIO RECIÉN CREADO AUTOMÁTICAMENTE**
                        if (esUsuarioRecienCreadoAutomaticamente(usuarioEncontrado)) {
                            logger.info("🔄 Usuario fue creado automáticamente en esta sesión - Tratando como NO REGISTRADO para TyC");
                            return false; // Tratarlo como no registrado para efectos de TyC
                        }
                    }
                } catch (Exception e) {
                    logger.warning("Error buscando por documento: " + e.getMessage());
                }
            }
            
            // 3. Si no se encontró ningún usuario, definitivamente NO está registrado
            if (usuarioEncontrado == null) {
                logger.info("❌ Firmante NO registrado: " + firmante.getEmail());
                return false;
            }
            
            // 4. **VALIDACIÓN ESTRICTA DE CONFLICTOS DE DATOS**
            logger.info("🔍 Usuario encontrado por " + metodoEncontrado + ". Validando coincidencia de datos...");
            
            List<String> erroresCoincidencia = new ArrayList<>();
            
            // **VALIDAR EMAIL**: Siempre debe coincidir (es el identificador principal)
            if (usuarioEncontrado.getCorreoElectronico() == null || 
                !usuarioEncontrado.getCorreoElectronico().equalsIgnoreCase(firmante.getEmail())) {
                erroresCoincidencia.add("Email ingresado no coincide: '" + firmante.getEmail() + "'");
            }
            
            // **VALIDAR DOCUMENTO**: Solo si se proporciona Y NO es un valor por defecto o placeholder
            if (firmante.getNumeroDocumento() != null && 
                !firmante.getNumeroDocumento().trim().isEmpty() &&
                !esValorPorDefecto(firmante.getNumeroDocumento())) {
                
                if (usuarioEncontrado.getNumeroDocumento() == null || 
                    !usuarioEncontrado.getNumeroDocumento().equals(firmante.getNumeroDocumento())) {
                    erroresCoincidencia.add("Número documento ingresado no coincide: '" + firmante.getNumeroDocumento() + "'");
                }
            } else {
                logger.info("📝 Número documento no validado (no proporcionado o valor por defecto): '" + firmante.getNumeroDocumento() + "'");
            }
            
            // **VALIDAR NOMBRE**: Solo si se proporciona Y NO es un valor por defecto
            if (firmante.getNombreCompleto() != null && 
                !firmante.getNombreCompleto().trim().isEmpty() &&
                !esValorPorDefecto(firmante.getNombreCompleto())) {
                
                if (usuarioEncontrado.getNombreCompleto() == null || 
                    !usuarioEncontrado.getNombreCompleto().trim().equalsIgnoreCase(firmante.getNombreCompleto().trim())) {
                    erroresCoincidencia.add("Nombre completo ingresado no coincide: '" + firmante.getNombreCompleto() + "'");
                }
            } else {
                logger.info("📝 Nombre completo no validado (no proporcionado o valor por defecto): '" + firmante.getNombreCompleto() + "'");
            }
            
            // **VALIDAR CELULAR**: Solo si se proporciona Y NO es un valor por defecto
            if (firmante.getNumeroCelular() != null && 
                !firmante.getNumeroCelular().trim().isEmpty() &&
                !esValorPorDefecto(firmante.getNumeroCelular())) {
                
                if (usuarioEncontrado.getNumeroCelular() == null || 
                    !usuarioEncontrado.getNumeroCelular().equals(firmante.getNumeroCelular())) {
                    erroresCoincidencia.add("Número celular ingresado no coincide: '" + firmante.getNumeroCelular() + "'");
                }
            } else {
                logger.info("📝 Número celular no validado (no proporcionado o valor por defecto): '" + firmante.getNumeroCelular() + "'");
            }
            
            // **VALIDAR TIPO DOCUMENTO**: Solo si se proporciona Y NO es un valor por defecto
            if (firmante.getTipoDocumento() != null && 
                !firmante.getTipoDocumento().trim().isEmpty() &&
                !esValorPorDefecto(firmante.getTipoDocumento())) {
                
                String tipoDocumentoBD = usuarioEncontrado.getIdTipoDocumento() != null ? 
                    usuarioEncontrado.getIdTipoDocumento().getIdTipoDocumento() : null;
                
                if (tipoDocumentoBD == null || !tipoDocumentoBD.equals(firmante.getTipoDocumento())) {
                    erroresCoincidencia.add("Tipo documento ingresado no coincide: '" + firmante.getTipoDocumento() + "'");
                }
            } else {
                logger.info("📝 Tipo documento no validado (no proporcionado o valor por defecto): '" + firmante.getTipoDocumento() + "'");
            }
            
            // 5. **DETENER COMPLETAMENTE EL PROCESO SI HAY CONFLICTOS DE DATOS**
            if (!erroresCoincidencia.isEmpty()) {
                logger.severe("❌ CONFLICTO DE DATOS DETECTADO - DETENIENDO PROCESO COMPLETO");
                logger.severe("❌ Firmante: " + firmante.getEmail());
                logger.severe("❌ Usuario encontrado por " + metodoEncontrado + " (ID: " + usuarioEncontrado.getIdUsuario() + ")");
                logger.severe("❌ Conflictos detectados:");
                
                for (String error : erroresCoincidencia) {
                    logger.severe("   🔴 " + error);
                }
                
                // **LANZAR EXCEPCIÓN CRÍTICA** que detenga todo el proceso
                String mensajeError = "🚫 PROCESO DETENIDO por conflictos de datos: " + String.join("; ", erroresCoincidencia);
                
                logger.severe("🚫 LANZANDO EXCEPCIÓN PARA DETENER PROCESO COMPLETO");
                throw new FirmaException(mensajeError);
            }
            
            // 6. Si llegamos aquí, el usuario está registrado Y los datos coinciden perfectamente
            logger.info("✅ Firmante REGISTRADO con datos VALIDADOS: " + firmante.getEmail() + 
                       " (ID: " + usuarioEncontrado.getIdUsuario() + ", encontrado por: " + metodoEncontrado + ")");
            
            return true;
            
        } catch (FirmaException e) {
            // **RE-LANZAR INMEDIATAMENTE** las excepciones de validación para detener el proceso
            logger.severe("🚫 Re-lanzando excepción de validación: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.severe("❌ Error técnico verificando registro de firmante " + firmante.getEmail() + ": " + e.getMessage());
            e.printStackTrace();
            // En caso de error técnico, lanzar excepción para evitar procesamientos incorrectos
            throw new FirmaException("Error técnico verificando firmante " + firmante.getEmail() + ": " + e.getMessage());
        }
    }

    /**
     * Verifica ÚNICAMENTE si un firmante está registrado (sin validaciones estrictas de datos)
     * Solo para determinar si agregar plantillas TyC, no para validar conflictos
     */
    public boolean verificarSiFirmanteEstaRegistradoSolo(FirmanteOrdenDTO firmante) {
        try {
            logger.info("🔍 Verificación simple de registro para: " + firmante.getEmail());
            
            // 1. Buscar por email usando DAO directamente
            try {
                Optional<Usuario> usuarioPorEmail = usuarioDao.findByEmail(firmante.getEmail());
                if (usuarioPorEmail.isPresent()) {
                    Usuario usuario = usuarioPorEmail.get();
                    logger.info("✅ Usuario encontrado por email: " + firmante.getEmail());
                    
                    // **🎯 VERIFICAR SI ES UN USUARIO RECIÉN CREADO AUTOMÁTICAMENTE**
                    if (esUsuarioRecienCreadoAutomaticamente(usuario)) {
                        logger.info("🔄 Usuario fue creado automáticamente en esta sesión - Tratando como NO REGISTRADO para TyC");
                        return false; // Tratarlo como no registrado para efectos de TyC
                    }
                    
                    return true;
                }
            } catch (Exception e) {
                logger.warning("Error buscando por email en verificación simple: " + e.getMessage());
            }
            
            // 2. Si no se encontró por email y tiene número de documento, buscar por documento
            if (firmante.getNumeroDocumento() != null && !firmante.getNumeroDocumento().trim().isEmpty() &&
                !esValorPorDefecto(firmante.getNumeroDocumento())) {
                try {
                    Optional<Usuario> usuarioPorDocumento = usuarioDao.findByNumeroDocumento(firmante.getNumeroDocumento());
                    if (usuarioPorDocumento.isPresent()) {
                        Usuario usuario = usuarioPorDocumento.get();
                        logger.info("✅ Usuario encontrado por documento: " + firmante.getNumeroDocumento());
                        
                        // **🎯 VERIFICAR SI ES UN USUARIO RECIÉN CREADO AUTOMÁTICAMENTE**
                        if (esUsuarioRecienCreadoAutomaticamente(usuario)) {
                            logger.info("🔄 Usuario fue creado automáticamente en esta sesión - Tratando como NO REGISTRADO para TyC");
                            return false; // Tratarlo como no registrado para efectos de TyC
                        }
                        
                        return true;
                    }
                } catch (Exception e) {
                    logger.warning("Error buscando por documento en verificación simple: " + e.getMessage());
                }
            }
            
            // 3. Si no se encontró ningún usuario, NO está registrado
            logger.info("❌ Firmante NO registrado (verificación simple): " + firmante.getEmail());
            return false;
            
        } catch (Exception e) {
            logger.warning("Error en verificación simple de firmante " + firmante.getEmail() + ": " + e.getMessage());
            // En caso de error, asumir que NO está registrado para agregar TyC
            return false;
        }
    }

    /**
     * Verifica si hay firmantes no registrados en la lista
     */
    public boolean verificarSiHayFirmantesNoRegistrados(List<FirmanteOrdenDTO> firmantes) {
        if (firmantes == null || firmantes.isEmpty()) {
            return false;
        }
        
        try {
            for (FirmanteOrdenDTO firmante : firmantes) {
                try {
                    // **🎯 USAR VERIFICACIÓN SIMPLE SIN VALIDACIONES ESTRICTAS**
                    boolean esRegistrado = verificarSiFirmanteEstaRegistradoSolo(firmante);
                    if (!esRegistrado) {
                        logger.info("🔍 Firmante no registrado detectado: " + firmante.getEmail());
                        return true;
                    }
                } catch (Exception e) {
                    // Si hay error verificando, asumir que no está registrado
                    logger.info("🔍 Error verificando firmante " + firmante.getEmail() + ", asumiendo no registrado");
                    return true;
                }
            }
            
            logger.info("🔍 Todos los firmantes están registrados");
            return false;
            
        } catch (Exception e) {
            logger.warning("Error verificando firmantes no registrados: " + e.getMessage());
            // En caso de error, asumir que hay firmantes no registrados para ser conservador
            return true;
        }
    }

    /**
     * Procesa firmantes mixtos detectando automáticamente si están registrados
     */
    public void procesarFirmantesMixtos(List<FirmanteOrdenDTO> firmantes, Long idUsuarioSolicitante) throws FirmaException {
        try {
            logger.info("=== PROCESANDO FIRMANTES MIXTOS - DETECCIÓN AUTOMÁTICA ===");
            logger.info("Total firmantes: " + (firmantes != null ? firmantes.size() : 0));
            logger.info("Usuario solicitante: " + idUsuarioSolicitante);
            
            if (firmantes == null || firmantes.isEmpty()) {
                logger.info("No hay firmantes para procesar");
                return;
            }
            
            for (FirmanteOrdenDTO firmante : firmantes) {
                logger.info("🔍 Verificando firmante: " + firmante.getEmail());
                
                // **DETECTAR AUTOMÁTICAMENTE SI EL FIRMANTE ESTÁ REGISTRADO**
                // NO capturar FirmaException aquí - dejar que se propague para detener el proceso
                boolean esUsuarioRegistrado = verificarSiFirmanteEstaRegistrado(firmante);
                
                if (!esUsuarioRegistrado) {
                    logger.info("🔄 Firmante NO registrado detectado: " + firmante.getEmail());
                    
                    // Validar que tenga datos completos para registro
                    validarDatosFirmanteNoRegistrado(firmante);
                    
                    // Crear usuario permanente para este firmante CON REFERIDO
                    Long idUsuarioCreado = crearUsuarioPermanenteParaFirmante(firmante, idUsuarioSolicitante);
                    
                    logger.info("✅ Firmante no registrado procesado: " + firmante.getEmail() + 
                               " -> Usuario ID: " + idUsuarioCreado + 
                               " (Referido por: " + idUsuarioSolicitante + ")");
                    
                } else {
                    logger.info("ℹ️ Firmante ya registrado: " + firmante.getEmail());
                }
            }
            
            logger.info("✅ Procesamiento de firmantes mixtos completado");
            
        } catch (FirmaException e) {
            // **RE-LANZAR INMEDIATAMENTE** para detener todo el proceso
            logger.severe("🚫 Error crítico en procesamiento de firmantes - DETENIENDO PROCESO: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.severe("❌ Error inesperado procesando firmantes mixtos: " + e.getMessage());
            e.printStackTrace();
            throw new FirmaException("Error procesando firmantes mixtos: " + e.getMessage());
        }
    }

    /**
     * Valida datos de firmante no registrado
     */
    public void validarDatosFirmanteNoRegistrado(FirmanteOrdenDTO firmante) throws FirmaException {
        if (firmante.getEmail() == null || firmante.getEmail().trim().isEmpty()) {
            throw new FirmaException("Email es requerido para firmante");
        }
        
        if (firmante.getNombreCompleto() == null || firmante.getNombreCompleto().trim().isEmpty()) {
            throw new FirmaException("Nombre completo requerido para firmante no registrado: " + firmante.getEmail());
        }
        
        if (firmante.getNumeroCelular() == null || firmante.getNumeroCelular().trim().isEmpty()) {
            throw new FirmaException("Número celular requerido para firmante no registrado: " + firmante.getEmail());
        }
        
        if (firmante.getTipoDocumento() == null || firmante.getTipoDocumento().trim().isEmpty()) {
            throw new FirmaException("Tipo documento requerido para firmante no registrado: " + firmante.getEmail());
        }
        
        if (firmante.getNumeroDocumento() == null || firmante.getNumeroDocumento().trim().isEmpty()) {
            throw new FirmaException("Número documento requerido para firmante no registrado: " + firmante.getEmail());
        }
    }

    /**
    * Crea usuario permanente específico para un firmante no registrado - CORREGIDO PARA MANEJAR USUARIOS EXISTENTES
    */
    public Long crearUsuarioPermanenteParaFirmante(FirmanteOrdenDTO firmante, Long idUsuarioSolicitante) throws FirmaException {
        try {
            logger.info("=== CREANDO USUARIO PERMANENTE PARA FIRMANTE ===");
            logger.info("Firmante: " + firmante.getEmail() + " - " + firmante.getNombreCompleto());
            logger.info("Usuario solicitante (referido): " + idUsuarioSolicitante);
            
            // **1. VERIFICAR SI YA EXISTE USUARIO CON DATOS EXACTOS ANTES DE VALIDAR DUPLICADOS**
            try {
                Optional<Usuario> usuarioExistente = usuarioDao.findByEmail(firmante.getEmail());
                if (usuarioExistente.isPresent()) {
                    Usuario usuario = usuarioExistente.get();
                    logger.info("🔍 Usuario ya existe - Verificando si los datos coinciden exactamente...");
                    
                    // **VERIFICAR SI LOS DATOS COINCIDEN EXACTAMENTE**
                    if (validarDatosCoinciden(usuario, firmante)) {
                        logger.info("✅ Usuario ya existe con datos exactos - NO se requiere creación");
                        logger.info("   - ID Usuario: " + usuario.getIdUsuario());
                        logger.info("   - Email: " + usuario.getCorreoElectronico());
                        logger.info("   - Documento: " + usuario.getNumeroDocumento());
                        logger.info("   - Celular: " + usuario.getNumeroCelular());
                        logger.info("   - Fue creado automáticamente: " + esUsuarioRecienCreadoAutomaticamente(usuario));
                        return usuario.getIdUsuario(); // **RETORNAR EL ID DEL USUARIO EXISTENTE**
                    } else {
                        logger.warning("⚠️ Usuario existe pero con datos diferentes - Conflicto de datos");
                        throw new FirmaException("El usuario " + firmante.getEmail() + " ya existe pero con datos diferentes. " +
                                            "Verifique los datos de documento y celular.");
                    }
                }
            } catch (FirmaException e) {
                // Re-lanzar excepciones específicas de validación
                throw e;
            } catch (Exception e) {
                logger.warning("Error verificando usuario existente: " + e.getMessage());
            }
            
            // **2. VALIDAR DUPLICADOS SOLO SI NO EXISTE USUARIO CON DATOS EXACTOS**  
            validarUsuarioNoExiste(firmante);

            // **3. CREAR SOLICITUD TEMPORAL PARA REUTILIZAR MÉTODO EXISTENTE**
            SolicitudFirmaUnificadaDTO requestTemporal = new SolicitudFirmaUnificadaDTO();
            requestTemporal.setNombreInteresado(firmante.getNombreCompleto());
            requestTemporal.setEmailInteresado(firmante.getEmail());
            requestTemporal.setTelefonoInteresado(firmante.getNumeroCelular());
            requestTemporal.setTipoDocumentoInteresado(firmante.getTipoDocumento());
            requestTemporal.setNumeroDocumentoInteresado(firmante.getNumeroDocumento());
            requestTemporal.setFechaExpedicionDocumentoInteresado(firmante.getFechaExpedicionDocumento());
            
            // **ASIGNAR EL ID DEL USUARIO SOLICITANTE COMO REFERIDO**
            requestTemporal.setIdUsuario(idUsuarioSolicitante);
            
            // **4. CREAR USUARIO PERMANENTE**
            Usuario usuarioCreado = crearOObtenerUsuarioPermanente(requestTemporal);
            return usuarioCreado.getIdUsuario();
            
        } catch (FirmaException e) {
            logger.severe("Error creando usuario permanente para firmante: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.severe("Error inesperado creando usuario permanente para firmante: " + e.getMessage());
            throw new FirmaException("Error creando usuario permanente para firmante: " + e.getMessage());
        }
    }

    /**
    * Valida si los datos del usuario existente coinciden exactamente con los del firmante
    */
    private boolean validarDatosCoinciden(Usuario usuarioExistente, FirmanteOrdenDTO firmante) {
        try {
            logger.info("🔍 Validando coincidencia exacta de datos:");
            logger.info("   - Email BD: '" + usuarioExistente.getCorreoElectronico() + "' vs Firmante: '" + firmante.getEmail() + "'");
            logger.info("   - Documento BD: '" + usuarioExistente.getNumeroDocumento() + "' vs Firmante: '" + firmante.getNumeroDocumento() + "'");
            logger.info("   - Celular BD: '" + usuarioExistente.getNumeroCelular() + "' vs Firmante: '" + firmante.getNumeroCelular() + "'");
            logger.info("   - Nombre BD: '" + usuarioExistente.getNombreCompleto() + "' vs Firmante: '" + firmante.getNombreCompleto() + "'");
            
            // **VALIDAR EMAIL (obligatorio)**
            boolean emailCoincide = usuarioExistente.getCorreoElectronico() != null && 
                                usuarioExistente.getCorreoElectronico().equalsIgnoreCase(firmante.getEmail());
            
            // **VALIDAR DOCUMENTO**
            boolean documentoCoincide = false;
            if (firmante.getNumeroDocumento() != null && !firmante.getNumeroDocumento().trim().isEmpty() &&
                !esValorPorDefecto(firmante.getNumeroDocumento())) {
                documentoCoincide = usuarioExistente.getNumeroDocumento() != null && 
                                usuarioExistente.getNumeroDocumento().equals(firmante.getNumeroDocumento());
            } else {
                // Si no se proporciona documento o es valor por defecto, considerar como coincidente
                documentoCoincide = true;
                logger.info("   - Documento no validado (no proporcionado o valor por defecto)");
            }
            
            // **VALIDAR CELULAR**
            boolean celularCoincide = false;
            if (firmante.getNumeroCelular() != null && !firmante.getNumeroCelular().trim().isEmpty() &&
                !esValorPorDefecto(firmante.getNumeroCelular())) {
                celularCoincide = usuarioExistente.getNumeroCelular() != null && 
                                usuarioExistente.getNumeroCelular().equals(firmante.getNumeroCelular());
            } else {
                // Si no se proporciona celular o es valor por defecto, considerar como coincidente
                celularCoincide = true;
                logger.info("   - Celular no validado (no proporcionado o valor por defecto)");
            }
            
            // **VALIDAR NOMBRE**
            boolean nombreCoincide = false;
            if (firmante.getNombreCompleto() != null && !firmante.getNombreCompleto().trim().isEmpty() &&
                !esValorPorDefecto(firmante.getNombreCompleto())) {
                nombreCoincide = usuarioExistente.getNombreCompleto() != null && 
                                usuarioExistente.getNombreCompleto().trim().equalsIgnoreCase(firmante.getNombreCompleto().trim());
            } else {
                // Si no se proporciona nombre o es valor por defecto, considerar como coincidente
                nombreCoincide = true;
                logger.info("   - Nombre no validado (no proporcionado o valor por defecto)");
            }
            
            // **TODOS LOS DATOS DEBEN COINCIDIR**
            boolean todosCoinciden = emailCoincide && documentoCoincide && celularCoincide && nombreCoincide;
            
            logger.info("🔍 Resultado validación:");
            logger.info("   - Email: " + emailCoincide);
            logger.info("   - Documento: " + documentoCoincide);
            logger.info("   - Celular: " + celularCoincide);
            logger.info("   - Nombre: " + nombreCoincide);
            logger.info("   - TODOS COINCIDEN: " + todosCoinciden);
            
            return todosCoinciden;
            
        } catch (Exception e) {
            logger.warning("Error validando coincidencia de datos: " + e.getMessage());
            return false;
        }
    }

    /**
    * Crea o obtiene usuario PERMANENTE
    */
    public Usuario crearOObtenerUsuarioPermanente(SolicitudFirmaUnificadaDTO request) throws FirmaException {
        try {
            logger.info("=== CREANDO O OBTENIENDO USUARIO PERMANENTE EN BD ===");
            logger.info("Nombre: " + request.getNombreInteresado());
            logger.info("Email: " + request.getEmailInteresado());
            logger.info("ID Usuario Solicitante (referido): " + request.getIdUsuario());

            // 1. VERIFICAR SI YA EXISTE USUARIO CON ESE EMAIL
            try {
                Optional<Usuario> usuarioExistente = usuarioDao.findByEmail(request.getEmailInteresado());
                if (usuarioExistente.isPresent()) {
                    Usuario usuario = usuarioExistente.get();
                    logger.info("✅ Usuario ya existe con ID: " + usuario.getIdUsuario());

                    boolean datosCompatibles = validarDatosCompatiblesParaRequest(usuario, request);

                    if (datosCompatibles) {
                        logger.info("✅ Datos del usuario existente son compatibles - usando usuario existente");
                        registrarEnAdmUsuario(usuario);
                        return usuario;
                    } else {
                        logger.warning("⚠️ Usuario existe pero con datos incompatibles");
                        throw new FirmaException("El usuario " + request.getEmailInteresado() +
                                            " ya existe pero con datos incompatibles. Verifique los datos proporcionados.");
                    }
                }
            } catch (FirmaException e) {
                throw e;
            } catch (Exception e) {
                logger.info("📝 Error técnico verificando usuario existente (continuando con creación): " + e.getMessage());
            }

            // 2. BUSCAR TIPO DE DOCUMENTO
            TipoDocumento tipoDocumento = null;
            try {
                tipoDocumento = obtenerTipoDocumento(request.getTipoDocumentoInteresado());

                if (tipoDocumento == null) {
                    Optional<TipoDocumento> tipoCC = tipoDocumentoDao.findById("CC");
                    tipoDocumento = tipoCC.orElse(null);
                    logger.warning("⚠️ Tipo documento no encontrado, usando CC por defecto");
                }
            } catch (Exception e) {
                logger.warning("⚠️ Error buscando tipo documento, usando CC por defecto");
                Optional<TipoDocumento> tipoCC = tipoDocumentoDao.findById("CC");
                tipoDocumento = tipoCC.orElse(null);
            }

            // 3. CREAR NUEVO USUARIO PERMANENTE
            Usuario nuevoUsuario = new Usuario();

            nuevoUsuario.setNombreCompleto(request.getNombreInteresado());
            nuevoUsuario.setCorreoElectronico(request.getEmailInteresado().toLowerCase());
            nuevoUsuario.setNumeroCelular(request.getTelefonoInteresado());
            nuevoUsuario.setNumeroDocumento(request.getNumeroDocumentoInteresado());

            if (tipoDocumento != null) {
                nuevoUsuario.setIdTipoDocumento(tipoDocumento);
            }

            nuevoUsuario.setActivo(true);
            nuevoUsuario.setEstado(true);
            nuevoUsuario.setProcesoRegistro(true);
            nuevoUsuario.setVerificadoFuente(false);
            nuevoUsuario.setFirmadoTyc(false);

            if (request.getIdUsuario() != null && request.getIdUsuario() > 0) {
                nuevoUsuario.setIdReferido(request.getIdUsuario());
                logger.info("🔗 Asignando referido: " + request.getIdUsuario());
            } else {
                logger.warning("⚠️ No se puede asignar referido - ID usuario solicitante no válido");
            }

            String claveTextoPlano = "FIRMESE_" + System.currentTimeMillis();
            String claveHasheada;

            try {
                if (passwordEncoder != null) {
                    claveHasheada = passwordEncoder.encode(claveTextoPlano);
                    logger.info("✅ Clave hasheada con BCrypt para seguridad");
                } else {
                    claveHasheada = org.springframework.security.crypto.bcrypt.BCrypt.hashpw(
                        claveTextoPlano, 
                        org.springframework.security.crypto.bcrypt.BCrypt.gensalt()
                    );
                    logger.info("✅ Clave hasheada con BCrypt manual");
                }

            } catch (Exception e) {
                logger.severe("❌ Error hasheando clave: " + e.getMessage());
                claveHasheada = claveTextoPlano;
                logger.warning("⚠️ Usando clave en texto plano como fallback");
            }
            
            nuevoUsuario.setClave(claveHasheada);
            nuevoUsuario.setFechaExpedicionDocumento(Utilities.getFechaTextoADate(request.getFechaExpedicionDocumentoInteresado()));
            nuevoUsuario.setObervacionVerificacion("Pendiente verificar");

            Usuario usuarioGuardado = usuarioDao.save(nuevoUsuario);

            logger.info("✅ Usuario PERMANENTE creado exitosamente:");
            logger.info("   - ID: " + usuarioGuardado.getIdUsuario());
            logger.info("   - Nombre: " + usuarioGuardado.getNombreCompleto());
            logger.info("   - Email: " + usuarioGuardado.getCorreoElectronico());
            logger.info("   - Documento: " + usuarioGuardado.getNumeroDocumento());
            logger.info("   - Referido por: " + request.getIdUsuario());
            logger.info("   - Estado proceso_registro: " + usuarioGuardado.getProcesoRegistro());

            try {
                servicioService.crearServicio(usuarioGuardado);
                logger.info("✅ Servicio creado para usuario permanente");
            } catch (Exception e) {
                logger.warning("⚠️ No se pudo crear servicio (no crítico): " + e.getMessage());
            }

            // Registrar en adm_usuario (gestion_adm)
            registrarEnAdmUsuario(usuarioGuardado);

            return usuarioGuardado;

        } catch (FirmaException e) {
            logger.severe("❌ Error manejando usuario permanente: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.severe("❌ Error inesperado creando usuario permanente: " + e.getMessage());
            e.printStackTrace();
            throw new FirmaException("Error creando usuario permanente: " + e.getMessage());
        }
    }

    /**
     * Registra el usuario en la tabla adm_usuario de gestión_adm usando DAO directo
     */
    private void registrarEnAdmUsuario(Usuario usuario) {
        try {
            AdmUsuarioDf existente = admUsuarioDao.findByeMailUsuario(usuario.getCorreoElectronico());
            if (existente != null) {
                logger.info("ℹ️ Usuario ya existe en adm_usuario: " + usuario.getCorreoElectronico());
                return;
            }
            admUsuarioDao.insertarAdmUsuario(
                usuario.getCorreoElectronico(),
                usuario.getNombreCompleto(),
                usuario.getNumeroCelular(),
                "",
                usuario.getClave(),
                1,
                usuario.getCorreoElectronico(),
                "firmese",
                usuario.getIdUsuario()
            );
            logger.info("✅ Usuario registrado en adm_usuario (gestion_adm) por INSERT nativo: " + usuario.getCorreoElectronico());
        } catch (Exception ex) {
            logger.warning("⚠️ Error registrando usuario en adm_usuario: " + ex.getMessage());
        }
    }

    /**
    * Valida si los datos del usuario existente son compatibles con la solicitud
    */
    private boolean validarDatosCompatiblesParaRequest(Usuario usuarioExistente, SolicitudFirmaUnificadaDTO request) {
        try {
            logger.info("🔍 Validando compatibilidad de datos para request:");
            
            // **EMAIL DEBE COINCIDIR SIEMPRE**
            boolean emailCoincide = usuarioExistente.getCorreoElectronico() != null && 
                                usuarioExistente.getCorreoElectronico().equalsIgnoreCase(request.getEmailInteresado());
            
            // **VALIDAR OTROS CAMPOS SOLO SI SE PROPORCIONAN Y NO SON VALORES POR DEFECTO**
            boolean documentoCompatible = true;
            if (request.getNumeroDocumentoInteresado() != null && 
                !request.getNumeroDocumentoInteresado().trim().isEmpty() &&
                !esValorPorDefecto(request.getNumeroDocumentoInteresado())) {
                
                documentoCompatible = usuarioExistente.getNumeroDocumento() == null || 
                                    usuarioExistente.getNumeroDocumento().equals(request.getNumeroDocumentoInteresado());
            }
            
            boolean celularCompatible = true;
            if (request.getTelefonoInteresado() != null && 
                !request.getTelefonoInteresado().trim().isEmpty() &&
                !esValorPorDefecto(request.getTelefonoInteresado())) {
                
                celularCompatible = usuarioExistente.getNumeroCelular() == null || 
                                usuarioExistente.getNumeroCelular().equals(request.getTelefonoInteresado());
            }
            
            boolean nombreCompatible = true;
            if (request.getNombreInteresado() != null && 
                !request.getNombreInteresado().trim().isEmpty() &&
                !esValorPorDefecto(request.getNombreInteresado())) {
                
                nombreCompatible = usuarioExistente.getNombreCompleto() == null || 
                                usuarioExistente.getNombreCompleto().trim().equalsIgnoreCase(request.getNombreInteresado().trim());
            }
            
            boolean todosCompatibles = emailCoincide && documentoCompatible && celularCompatible && nombreCompatible;
            
            logger.info("🔍 Resultado compatibilidad:");
            logger.info("   - Email: " + emailCoincide);
            logger.info("   - Documento: " + documentoCompatible);
            logger.info("   - Celular: " + celularCompatible);
            logger.info("   - Nombre: " + nombreCompatible);
            logger.info("   - TODOS COMPATIBLES: " + todosCompatibles);
            
            return todosCompatibles;
            
        } catch (Exception e) {
            logger.warning("Error validando compatibilidad: " + e.getMessage());
            return false;
        }
    }

    /**
     * Valida que no existan duplicados ANTES de crear usuario
     */
    private void validarUsuarioNoExiste(FirmanteOrdenDTO firmante) throws FirmaException {
        List<String> conflictos = new ArrayList<>();
        
        try {
            logger.info("🔍 Validando que el firmante no tenga datos duplicados...");
            logger.info("   - Email: " + firmante.getEmail());
            logger.info("   - Documento: " + firmante.getNumeroDocumento());
            logger.info("   - Celular: " + firmante.getNumeroCelular());
            
            // Validar email único 
            if (firmante.getEmail() != null && !firmante.getEmail().trim().isEmpty()) {
                try {
                    Optional<Usuario> usuarioPorEmail = usuarioDao.findByEmail(firmante.getEmail());
                    if (usuarioPorEmail.isPresent()) {
                        conflictos.add("Email ya registrado: " + firmante.getEmail());
                        logger.warning("⚠️ Email duplicado encontrado: " + firmante.getEmail());
                    }
                } catch (Exception e) {
                    logger.warning("Error validando email: " + e.getMessage());
                }
            }
            
            // Validar documento único
            if (firmante.getNumeroDocumento() != null && 
                !firmante.getNumeroDocumento().trim().isEmpty() && 
                !esValorPorDefecto(firmante.getNumeroDocumento())) {
                
                try {
                    Optional<Usuario> usuarioPorDoc = usuarioDao.findByNumeroDocumento(firmante.getNumeroDocumento());
                    if (usuarioPorDoc.isPresent()) {
                        conflictos.add("Número de documento ya registrado: " + firmante.getNumeroDocumento());
                        logger.warning("⚠️ Documento duplicado encontrado: " + firmante.getNumeroDocumento());
                    }
                } catch (Exception e) {
                    logger.warning("Error validando documento: " + e.getMessage());
                }
            }
            
            // Validar celular único
            if (firmante.getNumeroCelular() != null && 
                !firmante.getNumeroCelular().trim().isEmpty() && 
                !esValorPorDefecto(firmante.getNumeroCelular())) {
                
                try {
                    Optional<Usuario> usuarioPorCelular = usuarioDao.findByNumeroCelular(firmante.getNumeroCelular());
                    if (usuarioPorCelular.isPresent()) {
                        conflictos.add("Número celular ya registrado: " + firmante.getNumeroCelular());
                        logger.warning("⚠️ Celular duplicado encontrado: " + firmante.getNumeroCelular());
                    }
                } catch (Exception e) {
                    logger.warning("Error validando celular: " + e.getMessage());
                }
            }
            
            // Si hay conflictos, lanzar excepción
            if (!conflictos.isEmpty()) {
                String mensajeError = "🚫 PROCESO DETENIDO por datos duplicados: " + String.join("; ", conflictos);
                logger.severe("❌ " + mensajeError);
                throw new FirmaException(mensajeError);
            }
            
            logger.info("✅ Validación de duplicados completada - No se encontraron conflictos");
            
        } catch (FirmaException e) {
            // Re-lanzar excepciones de validación
            throw e;
        } catch (Exception e) {
            logger.severe("❌ Error técnico validando duplicados: " + e.getMessage());
            throw new FirmaException("Error técnico validando duplicados: " + e.getMessage());
        }
    }

    /**
     * Determina si un usuario fue creado automáticamente en esta sesión/proceso
     * CORREGIDO: Usuarios creados automáticamente pero activos y no en proceso de registro se consideran REGISTRADOS
     */
    private boolean esUsuarioRecienCreadoAutomaticamente(Usuario usuario) {
        try {
            if (usuario == null) return false;
            // Si el usuario está activo y NO está en proceso de registro, es REGISTRADO
            if (usuario.getActivo() && !usuario.getProcesoRegistro()) {
                logger.info("✅ Usuario creado automáticamente pero activo y no en proceso de registro - TRATADO COMO REGISTRADO");
                return false;
            }
            // Si la observación indica creación automática y está en proceso de registro, tratar como NO REGISTRADO
            String obs = usuario.getObervacionVerificacion();
            if (obs != null && obs.toLowerCase().contains("automáticamente")) {
                logger.info("🎯 Usuario identificado como creado automáticamente por observación");
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.warning("Error verificando si usuario es recién creado: " + e.getMessage());
            return false;
        }
    }

    /**
     * Determina si un valor es un placeholder o valor por defecto
     */
    private boolean esValorPorDefecto(String valor) {
        if (valor == null || valor.trim().isEmpty()) {
            return true;
        }
        
        // Valores comunes que indican que es un placeholder/defecto
        String valorLimpio = valor.trim().toLowerCase();
        
        // IDs numéricos pequeños (como "4", "1", etc.) probablemente son IDs, no documentos reales
        if (valorLimpio.matches("^\\d{1,3}$")) {
            return true;
        }
        
        // Números de teléfono con patrones de prueba
        if (valorLimpio.matches("^0+$") || // Solo ceros
            valorLimpio.equals("1234567890") ||
            valorLimpio.equals("0000000000") ||
            valorLimpio.matches("^(123|000|999).*")) {
            return true;
        }
        
        // Nombres de prueba comunes
        if (valorLimpio.contains("test") || 
            valorLimpio.contains("prueba") ||
            valorLimpio.contains("ejemplo") ||
            valorLimpio.contains("default")) {
            return true;
        }
        
        return false;
    }

    /**
     * Obtiene tipo de documento por ID
     */
    private TipoDocumento obtenerTipoDocumento(String idTipoDocumento) {
        try {
            Optional<TipoDocumento> tipoDoc = tipoDocumentoDao.findById(idTipoDocumento);
            return tipoDoc.orElse(null);
        } catch (Exception e) {
            logger.warning("Error obteniendo tipo documento " + idTipoDocumento + ": " + e.getMessage());
            return null;
        }
    }

    public Usuario obtenerUsuarioPorEmail(String email) {
        try {
            Optional<Usuario> usuarioOpt = usuarioDao.findByEmail(email);
            return usuarioOpt.orElse(null);
        } catch (Exception e) {
            logger.warning("Error obteniendo usuario por email: " + e.getMessage());
            return null;
        }
    }

}