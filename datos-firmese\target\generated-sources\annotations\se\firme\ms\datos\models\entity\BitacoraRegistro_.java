package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.Usuario;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(BitacoraRegistro.class)
public class BitacoraRegistro_ { 

    public static volatile SingularAttribute<BitacoraRegistro, String> numeroCelular;
    public static volatile SingularAttribute<BitacoraRegistro, String> codigoSms;
    public static volatile SingularAttribute<BitacoraRegistro, Date> fechaRegistro;
    public static volatile SingularAttribute<BitacoraRegistro, Usuario> idUsuario;
    public static volatile SingularAttribute<BitacoraRegistro, Long> idBitacoraRegistro;
    public static volatile SingularAttribute<BitacoraRegistro, String> correoElectronico;

}