package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.Usuario;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-23T18:28:34")
@StaticMetamodel(FirmaArchivoUsuario.class)
public class FirmaArchivoUsuario_ { 

    public static volatile SingularAttribute<FirmaArchivoUsuario, String> rutaRelativaArchivo;
    public static volatile SingularAttribute<FirmaArchivoUsuario, String> agenteNavegador;
    public static volatile SingularAttribute<FirmaArchivoUsuario, Date> fechaRegistro;
    public static volatile SingularAttribute<FirmaArchivoUsuario, Usuario> idUsuario;
    public static volatile SingularAttribute<FirmaArchivoUsuario, String> ip;
    public static volatile SingularAttribute<FirmaArchivoUsuario, String> hashArchivo;
    public static volatile SingularAttribute<FirmaArchivoUsuario, Long> idFirmaArchivoUsuario;
    public static volatile SingularAttribute<FirmaArchivoUsuario, String> nombreArchivoFirma;
    public static volatile SingularAttribute<FirmaArchivoUsuario, Boolean> subio;
    public static volatile SingularAttribute<FirmaArchivoUsuario, ArchivoFirma> idArchivoFirma;

}