package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.Usuario;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(ProcesoFirma.class)
public class ProcesoFirma_ { 

    public static volatile SingularAttribute<ProcesoFirma, String> codigoSms;
    public static volatile SingularAttribute<ProcesoFirma, Date> fechaRegistro;
    public static volatile SingularAttribute<ProcesoFirma, Date> fechaVencimiento;
    public static volatile SingularAttribute<ProcesoFirma, Usuario> idUsuario;
    public static volatile SingularAttribute<ProcesoFirma, Long> idProcesoFirma;
    public static volatile SingularAttribute<ProcesoFirma, Boolean> activo;
    public static volatile SingularAttribute<ProcesoFirma, String> token;

}