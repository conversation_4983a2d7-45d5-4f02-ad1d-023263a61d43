package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(UsuarioTemporal.class)
public class UsuarioTemporal_ { 

    public static volatile SingularAttribute<UsuarioTemporal, Date> fechaRegistro;
    public static volatile SingularAttribute<UsuarioTemporal, String> documentoPersona;
    public static volatile SingularAttribute<UsuarioTemporal, Long> idUsuario;
    public static volatile SingularAttribute<UsuarioTemporal, String> idTipoDocumento;
    public static volatile SingularAttribute<UsuarioTemporal, String> rowData;
    public static volatile SingularAttribute<UsuarioTemporal, String> numeroDocumento;
    public static volatile SingularAttribute<UsuarioTemporal, String> nombreCompleto;
    public static volatile SingularAttribute<UsuarioTemporal, Long> idReferido;

}