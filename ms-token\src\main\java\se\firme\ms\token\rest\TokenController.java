/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.token.rest;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.RegistroDTO;
import se.firme.ms.token.negocio.TokenNegocio;

/**
 * @document TokenController
 * <AUTHOR>
 * @fecha martes, agosto 18 de 2020, 02:21:37 PM
 */
@RestController
@RefreshScope
@RequestMapping("/registro")
public class TokenController {

	@Autowired
	private TokenNegocio tokenNegocio;

	@PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<ApiResponse> consultar(@RequestParam("codigo") String codigo) {
		try {
			RegistroDTO registroDTO = tokenNegocio.consultarToken(codigo);
			return new ResponseEntity<>(new ApiResponse.ok().data(registroDTO).build(), HttpStatus.OK);
		} catch (FirmaException e) {
			return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
					HttpStatus.BAD_REQUEST);
		}
	}

	@PostMapping(path = "/solicitud-cambio", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<ApiResponse> solicitudCambioContrasena(@RequestBody String email) {
		try {
			return new ResponseEntity<>(
					new ApiResponse.ok().data(tokenNegocio.crearTokenCambioContrasena(email)).build(),
					HttpStatus.OK);
		} catch (Exception e) {
			return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
					HttpStatus.BAD_REQUEST);
		}
	}
	
	 @PostMapping(path="/verifica-token-firmante", produces = MediaType.APPLICATION_JSON_VALUE)
	    public ResponseEntity<ApiResponse> verificarTokenFirmante(@RequestBody String token, HttpServletRequest request) {
	        try {
	            String ipAddress = request.getHeader("X-FORWARDED-FOR");
	            if (ipAddress == null) {
	                ipAddress = request.getRemoteAddr();
	            }
	            return new ResponseEntity<>(new ApiResponse.ok().data(tokenNegocio.obtenerDatosUsuariosParticipantesFirma(token)).build(), HttpStatus.OK);
	        } catch (Exception e) {
	            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
	        }
	    }
	 //Endpoint para mostrar los datos de usuario registrado por Oris
	 @PostMapping(path="/v2/verifica-token-firmante", produces = MediaType.APPLICATION_JSON_VALUE)
	    public ResponseEntity<ApiResponse> obtenerDatosSolicitudFirmaPorToken(@RequestBody String token, HttpServletRequest request) {
			// 1. Validar que el token no sea null o vacío
        	if (token == null || token.trim().isEmpty()) {
				return new ResponseEntity<>(
					new ApiResponse.error().mensaje("El token es requerido y no puede estar vacío").build(), 
					HttpStatus.BAD_REQUEST);
        	}
	        try {
	            String ipAddress = request.getHeader("X-FORWARDED-FOR");
	            if (ipAddress == null) {
	                ipAddress = request.getRemoteAddr();
	            }
	            return new ResponseEntity<>(new ApiResponse.ok().data(tokenNegocio.obtenerDatosUsuariosParticipantesFirma(token)).build(), HttpStatus.OK);
	        } catch (Exception e) {
	            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
	        }
	    }
	 
	 @PostMapping(path="/aceptar-token-firmante", produces = MediaType.APPLICATION_JSON_VALUE)
	    public ResponseEntity<ApiResponse> aceptarTokenFirmante(@RequestBody String token, HttpServletRequest request) {
	        try {
	            String ipAddress = request.getHeader("X-FORWARDED-FOR");
	            if (ipAddress == null) {
	                ipAddress = request.getRemoteAddr();
	            }
	           
	            return new ResponseEntity<>(new ApiResponse.ok().data( tokenNegocio.aceptarTokenFirmante(token)).build(), HttpStatus.OK);
	        } catch (Exception e) {
	            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
	        }
	    }
	 
	 @PostMapping(path="/solicitar-token-registro",produces = MediaType.APPLICATION_JSON_VALUE)
		public ResponseEntity<ApiResponse> solicitarNuevoToken(@RequestParam("codigo") String codigo) {
			try {
				tokenNegocio.solicitarNuevoToken(codigo);
				return new ResponseEntity<>(new ApiResponse.ok().data("Solicitud enviada").build(), HttpStatus.OK);
			} catch (FirmaException e) {
				return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
						HttpStatus.BAD_REQUEST);
			}
		}

}
