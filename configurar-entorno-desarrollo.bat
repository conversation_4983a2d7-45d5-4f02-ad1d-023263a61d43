@echo off
echo ========================================
echo   CONFIGURACION VARIABLES DE ENTORNO
echo   FIRMESE - DESARROLLO LOCAL
echo ========================================
echo.

REM Configurar variables de entorno para la sesión actual
echo 🔧 Configurando variables de entorno...

REM Ruta de archivos para Windows
set ROUTES_CUSTOM_FILE=C:/Users/<USER>/firmese/file
echo ✅ ROUTES_CUSTOM_FILE=%ROUTES_CUSTOM_FILE%

REM Configuración de base de datos
set SPRING_DATASOURCE_URL=**************************************************************************************************************
set SPRING_DATASOURCE_USERNAME=root
set SPRING_DATASOURCE_PASSWORD=root
echo ✅ Variables de base de datos configuradas

REM Configuración de logging
set LOGGING_LEVEL_SE_FIRME_MS_MODELS_SERVICE_PROCESOFIRMASERVICEIMPL=DEBUG
echo ✅ Logging configurado

REM Perfil activo
set SPRING_PROFILES_ACTIVE=dev
echo ✅ Perfil activo: dev

echo.
echo 📁 Creando estructura de directorios...

REM Crear directorios necesarios
mkdir "%ROUTES_CUSTOM_FILE%" 2>nul
mkdir "%ROUTES_CUSTOM_FILE%\1" 2>nul
mkdir "%ROUTES_CUSTOM_FILE%\2" 2>nul
mkdir "%ROUTES_CUSTOM_FILE%\3" 2>nul
mkdir "%ROUTES_CUSTOM_FILE%\tmp" 2>nul

REM Crear directorios por fecha actual
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "datestamp=%YYYY%%MM%%DD%"

mkdir "%ROUTES_CUSTOM_FILE%\1\%datestamp%" 2>nul
mkdir "%ROUTES_CUSTOM_FILE%\2\%datestamp%" 2>nul
mkdir "%ROUTES_CUSTOM_FILE%\3\%datestamp%" 2>nul

echo ✅ Directorios creados para fecha: %datestamp%

echo.
echo 📄 Creando archivos de prueba...

REM Crear algunos archivos de prueba
echo ARCHIVO DE PRUEBA PARA DESARROLLO LOCAL > "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf"
echo Fecha: %date% %time% >> "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf"
echo Este archivo fue generado automáticamente para pruebas. >> "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf"

echo ARCHIVO DE PRUEBA PARA DESARROLLO LOCAL > "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 8.pdf"
echo Fecha: %date% %time% >> "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 8.pdf"
echo Este archivo fue generado automáticamente para pruebas. >> "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 8.pdf"

echo ✅ Archivos de prueba creados

echo.
echo 🚀 COMANDOS PARA EJECUTAR LA APLICACION:
echo.
echo Para compilar:
echo    mvn clean compile
echo.
echo Para ejecutar con variables de entorno:
echo    mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Droutes.custom.file=C:/Users/<USER>/firmese/file -Dspring.profiles.active=dev"
echo.
echo Para ejecutar con perfil de desarrollo:
echo    mvn spring-boot:run -Dspring.profiles.active=dev
echo.
echo ========================================
echo   CONFIGURACION COMPLETADA
echo ========================================
echo.
echo 💡 NOTA: Las variables de entorno están configuradas solo para esta sesión.
echo    Para hacerlas permanentes, agrégalas al sistema desde Panel de Control.
echo.
pause
