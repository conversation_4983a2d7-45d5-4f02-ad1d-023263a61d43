@echo off
echo ========================================
echo   FIRMESE - EJECUCION DESARROLLO LOCAL
echo ========================================
echo.

REM Configurar variables de entorno para esta sesión
set ROUTES_CUSTOM_FILE=C:/Users/<USER>/firmese/file
set SPRING_PROFILES_ACTIVE=dev
set LOGGING_LEVEL_SE_FIRME_MS_MODELS_SERVICE_PROCESOFIRMASERVICEIMPL=DEBUG

echo 🔧 Variables de entorno configuradas:
echo    ROUTES_CUSTOM_FILE=%ROUTES_CUSTOM_FILE%
echo    SPRING_PROFILES_ACTIVE=%SPRING_PROFILES_ACTIVE%
echo.

REM Crear estructura de directorios si no existe
echo 📁 Verificando estructura de directorios...
if not exist "%ROUTES_CUSTOM_FILE%" mkdir "%ROUTES_CUSTOM_FILE%"
if not exist "%ROUTES_CUSTOM_FILE%\1" mkdir "%ROUTES_CUSTOM_FILE%\1"
if not exist "%ROUTES_CUSTOM_FILE%\tmp" mkdir "%ROUTES_CUSTOM_FILE%\tmp"

REM Crear directorio para fecha actual
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "datestamp=%YYYY%%MM%%DD%"

if not exist "%ROUTES_CUSTOM_FILE%\1\%datestamp%" mkdir "%ROUTES_CUSTOM_FILE%\1\%datestamp%"

echo ✅ Directorios verificados para fecha: %datestamp%

REM Crear archivos de prueba si no existen
if not exist "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf" (
    echo ARCHIVO DE PRUEBA PARA DESARROLLO LOCAL > "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf"
    echo Fecha: %date% %time% >> "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf"
    echo ✅ Archivo de prueba 7 creado
)

if not exist "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 8.pdf" (
    echo ARCHIVO DE PRUEBA PARA DESARROLLO LOCAL > "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 8.pdf"
    echo Fecha: %date% %time% >> "%ROUTES_CUSTOM_FILE%\1\%datestamp%\SIGNED_Pruebas notificacion 8.pdf"
    echo ✅ Archivo de prueba 8 creado
)

echo.
echo 🚀 Iniciando aplicación Firmese...
echo.

REM Cambiar al directorio del proyecto datos-firmese
cd /d "datos-firmese"

REM Ejecutar la aplicación con las variables de entorno configuradas
mvn spring-boot:run ^
    -Dspring-boot.run.jvmArguments="-Droutes.custom.file=C:/Users/<USER>/firmese/file -Dspring.profiles.active=dev -Dlogging.level.se.firme.ms.models.service.ProcesoFirmaServiceImpl=DEBUG"

echo.
echo ========================================
echo   APLICACION FINALIZADA
echo ========================================
pause
