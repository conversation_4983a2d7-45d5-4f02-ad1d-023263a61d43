package se.firme.ms.datos.models.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-23T18:28:34")
@StaticMetamodel(AdmUsuarioDf.class)
public class AdmUsuarioDf_ { 

    public static volatile SingularAttribute<AdmUsuarioDf, String> telefonoUsuario;
    public static volatile SingularAttribute<AdmUsuarioDf, String> direccionUsuario;
    public static volatile SingularAttribute<AdmUsuarioDf, String> eMailUsuario;
    public static volatile SingularAttribute<AdmUsuarioDf, Long> idUsuarioEnCliente;
    public static volatile SingularAttribute<AdmUsuarioDf, String> claveUsuario;
    public static volatile SingularAttribute<AdmUsuarioDf, Long> idUsuario;
    public static volatile SingularAttribute<AdmUsuarioDf, String> nombreUsuario;
    public static volatile SingularAttribute<AdmUsuarioDf, Integer> activoUsuario;
    public static volatile SingularAttribute<AdmUsuarioDf, String> client_id;

}