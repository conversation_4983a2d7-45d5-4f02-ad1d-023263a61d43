/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.*;

import org.hibernate.LazyInitializationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.zxing.WriterException;

import se.firme.commons.exception.FirmaException;
import se.firme.commons.exception.ServicioFirmeseException;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.firmese.service.EmailService;
import se.firme.commons.firmese.service.EmailTemplateService;
import se.firme.commons.firmese.service.GeneratePDF;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.dao.IProcesoFirmaDao;
import se.firme.ms.datos.models.dao.IUsuarioDao;
import se.firme.ms.datos.models.dao.IUsuarioFirmaArchivoOtpDao;
import se.firme.ms.datos.models.entity.*;
import se.firme.ms.models.service.helper.ArchivoFirmaHelper;
import se.firme.ms.models.service.helper.FirmaArchivoUsuarioHelper;
import se.firme.ms.utils.UserParameter;

/**
 *
 * <AUTHOR> Armando Gonzalez Ramirez <<EMAIL>>
 */
@Component
public class ProcesoFirmaServiceImpl {

    @Autowired
    IProcesoFirmaDao dao;
    @Autowired
    private ArchivoFirmaServiceImpl archivoFirmaDao;
    @Autowired
    private IUsuarioDao iUsuarioDao;
    @Autowired
    private ServicioService servicioService;
    @Autowired
    private FirmaArchivoUsuarioServiceImpl firmaArchivoService;
    @Autowired
    private TokenServiceImpl tokenService;
    @Autowired
    private SolicitudFirmaService solicitudFirmaService;
    @Autowired
    private Environment env;
    @Autowired
    private ParametroServiceImpl parametroServiceImpl;
    @Autowired
    private IUsuarioFirmaArchivoOtpDao usuarioFirmaArchivoOtpDao;
    @Autowired
    private TerminosCondicionesService terminosCondicionesService;
    
    @Autowired
    private FirmaOrdenService firmaOrdenService;

    private static Logger logger = LoggerFactory.getLogger(ProcesoFirmaServiceImpl.class.getName());

    @Transactional
    public ProcesoFirma getNuevoProcesoFirma(Usuario usuario) {
        return getNuevoProcesoFirma(usuario, null);
    }

    @Transactional
    public ProcesoFirma getNuevoProcesoFirma(Usuario usuario, String token) {
        try {
            // Desactivar cualquier proceso OTP activo para el usuario
            ProcesoFirma procesoExistente = dao.findValidOtpByUsuario(usuario.getIdUsuario());
            if (procesoExistente != null) {
                desactivarProcesoFirmaBySMSIdUSuario(procesoExistente.getCodigoSms(), usuario.getIdUsuario());
            }

            // Crear uno nuevo SIEMPRE
            ProcesoFirma prc = new ProcesoFirma();
            Date fecha = new Date();
            prc.setIdUsuario(usuario);
            prc.setFechaRegistro(fecha);
            prc.setFechaVencimiento(Utilities.getSumarMinutosHorasDiasAFecha(fecha, 0, 24, 0));
            prc.setActivo(true);
            prc.setCodigoSms(getCodigoSMS(6));
            prc.setToken(token);
            prc = dao.save(prc);

            // Asociar archivos del token con el nuevo proceso
            List<String> idsArchivos = extraerArchivosDelToken(token);
            if (idsArchivos != null && !idsArchivos.isEmpty()) {
                actualizarIdProcesoArchivoFirma(prc.getIdProcesoFirma(), idsArchivos);
            }

            logger.info("Creado nuevo proceso OTP para usuario: " + usuario.getIdUsuario() +
                    " código: " + prc.getCodigoSms());
            return prc;
        } catch (FirmaException e) {
            logger.error("Error desactivando proceso OTP anterior: " + e.getMessage());
            ProcesoFirma prc = new ProcesoFirma();
            Date fecha = new Date();
            prc.setIdUsuario(usuario);
            prc.setFechaRegistro(fecha);
            prc.setFechaVencimiento(Utilities.getSumarMinutosHorasDiasAFecha(fecha, 0, 24, 0));
            prc.setActivo(true);
            prc.setCodigoSms(getCodigoSMS(6));
            prc.setToken(token);
            prc = dao.save(prc);

            List<String> idsArchivos = extraerArchivosDelToken(token);
            if (idsArchivos != null && !idsArchivos.isEmpty()) {
                actualizarIdProcesoArchivoFirma(prc.getIdProcesoFirma(), idsArchivos);
            }

            logger.info("Creado nuevo proceso OTP para usuario (tras excepción): " + usuario.getIdUsuario() +
                    " código: " + prc.getCodigoSms());
            return prc;
        }
    }

    private String getCodigoSMS(int longitud) {
        String rta = "";

        int contadorIntentos = 0;
        boolean valido = false;

        while (!valido && contadorIntentos <= 20) {
            String codigo = Utilities.replace(Utilities.generarToken(longitud));

            List<ProcesoFirma> tokens = findByCodSMSUnico(codigo);
            if (tokens == null || (tokens != null && tokens.isEmpty())) {
                valido = true;
                rta = codigo;
            }

            contadorIntentos++;
            System.err.println("Intento No. " + contadorIntentos + " para determinar el codigo de transaccion");
        }

        return rta;
    }

    public List<ProcesoFirma> findByCodSMSUnico(String codSMS) {
        return dao.findByCodSMSUnico(codSMS);
    }

    @Transactional
    public void actualizarIdProcesoArchivoFirma(long idProcesoFirma, List<String> idsArchivos) {
        for (String idArchivo : idsArchivos) {
            try {
                long archivoId = Long.parseLong(idArchivo);
                // Verificar si ya existe la asociación
                List<UsuarioFirmaArchivoOtp> existentes = usuarioFirmaArchivoOtpDao.findByIdProcesoFirma(idProcesoFirma);
                boolean yaExiste = existentes.stream()
                        .anyMatch(ufao -> ufao.getIdArchivoFirma().getIdArchivoFirma() == archivoId);
                
                if (!yaExiste) {
                    usuarioFirmaArchivoOtpDao.associateProcesoWithArchivo(idProcesoFirma, archivoId);
                    logger.info("Asociado archivo {} con proceso {}", archivoId, idProcesoFirma);
                } else {
                    logger.info("El archivo {} ya está asociado con el proceso {}", archivoId, idProcesoFirma);
                }
            } catch (NumberFormatException e) {
                logger.error("Error al convertir ID de archivo: {}", idArchivo, e);
            }
        }
    }

    public ProcesoFirma findByCodSMSIdUsuario(String codSMS, long idUsuario) {
        List<ProcesoFirma> lista = dao.findByCodSMSIdUsuario(codSMS, idUsuario);
        if (lista != null && lista.size() > 0) {
            return lista.get(0);
        } else {
            return null;
        }
    }

    public boolean isProcesoFirmaActivo(ProcesoFirma procesoFirma) throws FirmaException {
        if (procesoFirma != null) {
            Date date = new Date();
            if (procesoFirma.isActivo()) {
                if (date.before(procesoFirma.getFechaVencimiento())) {
                    return true;
                } else {
                    logger.info("Proceso OTP vencido pero mantiene estado activo - ID: {}, Vencimiento: {}", 
                               procesoFirma.getIdProcesoFirma(), procesoFirma.getFechaVencimiento());
                    return false;
                }
            }
        }
        return false;
    }

    @Transactional(readOnly = false, isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {
        Exception.class})
    public void desactivarProcesoFirmaBySMSIdUSuario(String codSMS, long idUsuario) throws FirmaException {
        try {
            logger.info("Código sms: " + codSMS + "  id usuario: " + idUsuario);
            
            dao.desactivarProcesoFirmaBySMSIdUSuario(codSMS, idUsuario);
            usuarioFirmaArchivoOtpDao.desactivarRegistrosPorOtp(codSMS, idUsuario);
            
        } catch (Exception e) {
            logger.error("Error while disabling process with sms code " + codSMS + " and user id " + idUsuario + " : "
                    + e.getMessage());
            throw new FirmaException("No se pudo actualizar el proceso de firma " + e.getMessage());
        }
    }

    @Transactional(readOnly = false, isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {
        FirmaException.class})
    public List<ArchivoFirmaResponseDTO> firmarDocumentos(String sms, long idUsuario, String token, String ipAddress,
            String userAgent) throws FirmaException {
        try {
            Optional<Usuario> usuario = iUsuarioDao.findById(idUsuario);
            ProcesoFirma proceso = findByCodSMSIdUsuario(sms, idUsuario);
            
            if (proceso != null && proceso.isActivo()) {
                Date date = new Date();
                
                // Verificar si está vencido AL MOMENTO DE USARLO
                if (!date.before(proceso.getFechaVencimiento())) {
                    // Desactivar porque se está intentando usar un código vencido
                    desactivarProcesoFirmaBySMSIdUSuario(sms, idUsuario);
                    throw new FirmaException("El código OTP ha vencido y ha sido desactivado");
                }
                
                // el proceso está activo y válido
                if (proceso.getToken() != null && !"".equals(proceso.getToken())) {
                    if (token.equals(proceso.getToken())) {
                        // Obtener archivos usando la nueva tabla usuario_firma_archivo_otp
                        List<UsuarioFirmaArchivoOtp> archivosOtp = usuarioFirmaArchivoOtpDao.findValidArchivosForOtp(sms, idUsuario);
                        
                        List<ArchivoFirma> lista = new ArrayList<>();
                        for (UsuarioFirmaArchivoOtp archivoOtp : archivosOtp) {
                            lista.add(archivoOtp.getIdArchivoFirma());
                        }
                        
                        if (lista.isEmpty()) {
                            throw new FirmaException("No se encontraron archivos válidos para el código OTP proporcionado");
                        }
                        
                        agregarFirmante(lista, sms, usuario.get(), ipAddress, userAgent);
                        
                        // Verificar y actualizar TyC si aplica
                        try {
                            verificarYActualizarTyC(token, usuario.get().getCorreoElectronico());
                        } catch (Exception e) {
                            logger.info("Error verificando TyC (no crítico): " + e.getMessage());
                        }

                        return getListaResponse(lista, usuario.get());
                    } else {
                        throw new FirmaException("Datos no coinciden en la validación de token");
                    }
                } else {
                    // Fallback al método anterior si no hay token
                    List<ArchivoFirma> lista = archivoFirmaDao.findByCodProcess(sms, idUsuario);
                    
                    if (lista == null || lista.isEmpty()) {
                        throw new FirmaException("No se encontraron archivos válidos para el código OTP proporcionado");
                    }
                    
                    agregarFirmante(lista, sms, usuario.get(), ipAddress, userAgent);
                    
                    // Verificar y actualizar TyC si aplica
                    try {
                        verificarYActualizarTyC(token, usuario.get().getCorreoElectronico());
                    } catch (Exception e) {
                        logger.info("Error verificando TyC (no crítico): " + e.getMessage());
                    }

                    return getListaResponse(lista, usuario.get());
                }
            }
            
            throw new FirmaException("Proceso de firma no válido o inactivo");
        } catch (FirmaException | NullPointerException e) {
            logger.error("Error while signing documents with sms code " + sms + " and user id " + idUsuario + " : "
                    + e.getMessage());
            throw new FirmaException("No se pudo completar el proceso de firma, " + e.getMessage());
        }
    }

    @Transactional(readOnly = false, isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {
        Exception.class})
    private void agregarFirmante(List<ArchivoFirma> lista, String csms, Usuario usuario, String ipAddress,
            String userAgent) {
        logger.info(usuario.toString());
        for (ArchivoFirma archivoFirma : lista) {
            boolean propietario = true;
            try {
                logger.info("Arhivo a firmar: " + archivoFirma.toString());
                propietario = (archivoFirma.getIdUsuario() == usuario.getIdUsuario());
                logger.info("Usuario " + usuario.getNombreCompleto() + " es propietario de documento " + archivoFirma.getNombreArchivo() + " " + propietario);
                servicioService.validarServicio(archivoFirma.getIdUsuario(), propietario);
                String rutaRelativa = archivoFirma.getRutaRelativaArchivo();
                String pathOriginalTemporal = getRutaArchivos(true) + rutaRelativa + archivoFirma.getNombreArchivo();
                logger.info("Ruta de Arhivo original: " + pathOriginalTemporal);
                File temporal = new File(pathOriginalTemporal);
                if (temporal.exists()) {
                    boolean aceptado = firmaArchivoService.guardarRegistro(archivoFirma, usuario, propietario,
                            archivoFirma.getHashArchivo(), "", "", ipAddress, userAgent);
                    if (propietario && !aceptado) {
                        logger.info("Esta firma ya tuvo que haber sido hecha....");
                        throw new FirmaException("Documento ya cuenta con la aceptación de firma por el usuario ");
                    }
                    desactivarProcesoFirmaBySMSIdUSuario(csms, usuario.getIdUsuario());
                    logger.info("Código de proceso desactivado");
                    archivoFirma.setCantidadFirmado(firmaArchivoService.contarfirmantes(archivoFirma.getIdArchivoFirma()));
                    archivoFirmaDao.actualizarCantidadFirmas(archivoFirma.getCantidadFirmado(), archivoFirma.getIdArchivoFirma());
                    completarFirmado(archivoFirma, rutaRelativa, pathOriginalTemporal, true);
                    
                    // **NUEVO**: Marcar solicitud como firmada
                    SolicitudFirma solicitudFirma = solicitudFirmaService.findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(archivoFirma.getIdArchivoFirma(), usuario.getCorreoElectronico());
                    if (solicitudFirma != null) {
                        solicitudFirma.setFirmado(true);
                        solicitudFirma.setFechaFirma(new Date());
                        solicitudFirmaService.save(solicitudFirma);
                        
                        // **CLAVE**: Verificar si se debe activar el siguiente orden
                        verificarYActivarSiguienteOrden(archivoFirma.getIdArchivoFirma(), solicitudFirma.getOrdenFirma(), solicitudFirma.getTipoOrdenFirma());
                    }
                } else {
                    archivoFirma.setResultadoFirma("No existe el archivo en el directorio asignado");
                }
            } catch (FirmaException | LazyInitializationException | IOException e) {
                logger.error("Error while signing document with id " + archivoFirma.getIdArchivoFirma() + " : " + e.getMessage());
                archivoFirma.setResultadoFirma(archivoFirma.getResultadoFirma() == null ? e.getMessage() : archivoFirma.getResultadoFirma());
                servicioService.retornarBolsa(archivoFirma.getIdUsuario(), propietario);
            } catch (ServicioFirmeseException e) {
                logger.error("Error while signing document with id " + archivoFirma.getIdArchivoFirma() + " : " + e.getMessage());
                archivoFirma.setResultadoFirma(e.getMessage());
            }
        }
    }

    /**
    * Verifica si se completó un orden y activa el siguiente en firma secuencial
    */
    private void verificarYActivarSiguienteOrden(Long idArchivoFirma, int ordenFirmado, String tipoOrden) {
        try {
            if (!"SECUENCIAL".equals(tipoOrden)) {
                logger.info("Documento no es secuencial, no se verifica siguiente orden");
                return;
            }
            
            logger.info("=== VERIFICANDO SIGUIENTE ORDEN SECUENCIAL ===");
            logger.info("Archivo ID: " + idArchivoFirma + ", Orden firmado: " + ordenFirmado);
            
            // **PRIMERO: OBTENER TODAS LAS SOLICITUDES PARA DEBUG COMPLETO**
            List<SolicitudFirma> todasSolicitudes = solicitudFirmaService.findAllByIdArchivoFirma(idArchivoFirma);
            logger.info("🔍 TOTAL de solicitudes en el archivo: " + todasSolicitudes.size());
            
            // **DEBUG: Mostrar todas las solicitudes con detalles**
            logger.info("📋 LISTADO COMPLETO DE SOLICITUDES:");
            for (SolicitudFirma sol : todasSolicitudes) {
                logger.info("   📋 ID: " + sol.getIdSolicitudFirma() + 
                        " | Email: " + sol.getEmailFirmante() + 
                        " | Orden: " + sol.getOrdenFirma() + 
                        " | Firmado: " + sol.isFirmado() + 
                        " | Tipo: " + sol.getTipoOrdenFirma() +
                        " | Fecha Firma: " + sol.getFechaFirma());
            }
            
            // **AGRUPAR POR ORDEN PARA ANÁLISIS**
            Map<Integer, List<SolicitudFirma>> solicitudesPorOrden = new HashMap<>();
            for (SolicitudFirma sol : todasSolicitudes) {
                int orden = sol.getOrdenFirma();
                solicitudesPorOrden.computeIfAbsent(orden, k -> new ArrayList<>()).add(sol);
            }
            
            logger.info("📊 ANÁLISIS POR ORDEN:");
            for (Map.Entry<Integer, List<SolicitudFirma>> entry : solicitudesPorOrden.entrySet()) {
                int orden = entry.getKey();
                List<SolicitudFirma> solicitudesOrden = entry.getValue();
                long firmadas = solicitudesOrden.stream().mapToLong(s -> s.isFirmado() ? 1 : 0).sum();
                long pendientes = solicitudesOrden.size() - firmadas;
                
                logger.info("   🎯 ORDEN " + orden + ": " + solicitudesOrden.size() + " total, " + 
                        firmadas + " firmadas, " + pendientes + " pendientes");
            }
            
            // **VERIFICAR EL ORDEN ACTUAL QUE SE ACABA DE FIRMAR**
            List<SolicitudFirma> solicitudesOrdenActual = solicitudesPorOrden.get(ordenFirmado);
            
            if (solicitudesOrdenActual == null || solicitudesOrdenActual.isEmpty()) {
                logger.warn("⚠️ NO HAY SOLICITUDES PARA EL ORDEN " + ordenFirmado + " - ESTO ES UN PROBLEMA");
                logger.warn("⚠️ Órdenes disponibles: " + solicitudesPorOrden.keySet());
                return;
            }
            
            // **VERIFICAR SI TODAS LAS SOLICITUDES DEL ORDEN ACTUAL ESTÁN FIRMADAS**
            boolean ordenCompletado = true;
            int pendientes = 0;
            
            logger.info("🔍 VERIFICANDO COMPLETITUD DEL ORDEN " + ordenFirmado + ":");
            for (SolicitudFirma solicitud : solicitudesOrdenActual) {
                logger.info("   - " + solicitud.getEmailFirmante() + ": " + 
                        (solicitud.isFirmado() ? "✅ FIRMADO" : "⏳ PENDIENTE"));
                if (!solicitud.isFirmado()) {
                    ordenCompletado = false;
                    pendientes++;
                }
            }
            
            logger.info("📊 RESULTADO ORDEN " + ordenFirmado + ":");
            logger.info("   - Total solicitudes: " + solicitudesOrdenActual.size());
            logger.info("   - Firmadas: " + (solicitudesOrdenActual.size() - pendientes));
            logger.info("   - Pendientes: " + pendientes);
            logger.info("   - ¿Orden completado?: " + (ordenCompletado ? "✅ SÍ" : "❌ NO"));
            
            if (ordenCompletado) {
                logger.info("🎉 ORDEN " + ordenFirmado + " COMPLETADO - PROCEDIENDO CON SIGUIENTE ORDEN");
                
                // **BUSCAR EL SIGUIENTE ORDEN**
                int siguienteOrden = ordenFirmado + 1;
                List<SolicitudFirma> solicitudesSiguienteOrden = solicitudesPorOrden.get(siguienteOrden);
                
                if (solicitudesSiguienteOrden == null || solicitudesSiguienteOrden.isEmpty()) {
                    logger.info("✅ NO HAY ORDEN " + siguienteOrden + " - PROCESO DE FIRMA SECUENCIAL COMPLETADO");
                    return;
                }
                
                logger.info("🚀 ACTIVANDO ORDEN " + siguienteOrden + " (" + 
                        solicitudesSiguienteOrden.size() + " firmante(s)):");
                
                for (SolicitudFirma sol : solicitudesSiguienteOrden) {
                    logger.info("   📧 " + sol.getEmailFirmante() + " (orden: " + sol.getOrdenFirma() + ")");
                }
                
                // **LLAMAR AL MÉTODO PARA GENERAR TOKENS PARA EL SIGUIENTE ORDEN**
                try {
                    firmaOrdenService.generarTokensSiguienteOrden(idArchivoFirma, ordenFirmado);
                    logger.info("✅ TOKENS GENERADOS EXITOSAMENTE PARA ORDEN " + siguienteOrden);
                } catch (Exception tokenEx) {
                    logger.error("❌ ERROR GENERANDO TOKENS PARA ORDEN " + siguienteOrden + ": " + tokenEx.getMessage());
                    tokenEx.printStackTrace();
                }
                
            } else {
                logger.info("⏳ ORDEN " + ordenFirmado + " AÚN NO COMPLETADO");
                logger.info("   Faltan " + pendientes + " firmante(s) por firmar");
                logger.info("   El proceso secuencial continuará cuando todos firmen");
            }
            
        } catch (Exception e) {
            logger.error("❌ ERROR CRÍTICO verificando siguiente orden: " + e.getMessage());
            e.printStackTrace();
            // No lanzar excepción para no afectar el flujo principal de firma
        }
    }

    private boolean generarDocumentoFirmado(String path, String pathOriginalTemporal, long idArchivoFirma, long idUsuario)
            throws FirmaException {
        try {
            List<FirmaArchivoUsuario> firmas = firmaArchivoService.findByIdArchivoFirma(idArchivoFirma);
            List<InputStream> streams = new ArrayList<>();
            ArchivoFirma archivoFirma = archivoFirmaDao.findById(idArchivoFirma);
            GeneratePDF gp = new GeneratePDF();

            if (parametroServiceImpl.verificarParametro(UserParameter.ADD_WATERMARK, idUsuario)) {
                pathOriginalTemporal = gp.addWatermarkToFile(pathOriginalTemporal);
            }
            if (parametroServiceImpl.verificarParametro(UserParameter.ADD_STAMP, idUsuario)) {
                Optional<FirmaArchivoUsuario> fiOptional = firmas.stream().filter(archivo -> archivo.getSubio() == true).findAny();
                if (fiOptional.isPresent()) {
                    FirmaArchivoUsuario archivoUsuario = fiOptional.get();
                    pathOriginalTemporal = gp.addStamp(archivoUsuario.getIdUsuario().getNombreCompleto(),
                            archivoUsuario.getIdUsuario().getIdTipoDocumento().getIdTipoDocumento() + " " + archivoUsuario.getIdUsuario().getNumeroDocumento(),
                            pathOriginalTemporal);
                }
            }
            if (parametroServiceImpl.verificarParametro(UserParameter.ADD_VERTICAL_STAMP, idUsuario)) {
                String firmante;
                if (firmas.size() == 1) {
                    firmante = firmas.get(0).getIdUsuario().getNombreCompleto();
                } else {
                    firmante = firmas.get(firmas.size() - 1).getIdUsuario().getNombreCompleto() + " y otros";
                }
                pathOriginalTemporal = gp.addVerticalStamp(firmante, archivoFirma.getHashArchivo(), pathOriginalTemporal);
            }
            streams.add(new FileInputStream(pathOriginalTemporal));

            ArchivoFirmaResponseDTO archivoFirmaResponseDTO = ArchivoFirmaHelper.getArchivoFirmaHandle(archivoFirma);
            archivoFirmaResponseDTO.setFirmas(FirmaArchivoUsuarioHelper.convert(firmas));
            String fileFirmantes = gp.createPDF(archivoFirmaResponseDTO,
                    env.getProperty("routes.custom.queryfile"));

            streams.add(new FileInputStream(fileFirmantes));
            String rutaPrincipalDestino = getRutaArchivos(false);
            Utilities.crearDirectorio(rutaPrincipalDestino + path, true);
            String fileName = "SIGNED_" + archivoFirma.getNombreArchivo();
            String rutaArhivoDestino = rutaPrincipalDestino + path + fileName;
            rutaArhivoDestino = rutaArhivoDestino.replace("original/", "");
            logger.info("Ruta de Arhivo Destino " + rutaArhivoDestino);
            OutputStream outputStream = new FileOutputStream(rutaArhivoDestino);
            GeneratePDF.mergePDF(streams, outputStream);
            String hash = Utilities.generarHash(rutaArhivoDestino);
            logger.info("Hash  " + hash); // guardar firma en db

            for (FirmaArchivoUsuario firma : firmas) {
                firmaArchivoService.actualizarFirma(hash, path.replace("original" + File.separator, ""), fileName,
                        firma.getIdFirmaArchivoUsuario());
            }
            return true;
        } catch (Exception e) {
            throw new FirmaException("No se pudo generar el documento firmado " + e.getMessage());
        }
    }

    @Deprecated
    @Transactional(readOnly = false, isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {
        Exception.class})
    private boolean procesarFirmasMultiples(List<ArchivoFirma> lista, String csms, Usuario usuario, String ipAddress,
            String userAgent) {
        logger.info(usuario.toString());
        for (ArchivoFirma archivoFirma : lista) {
            boolean propietario = true;
            try {
                logger.info("Arhivo a firmar: " + archivoFirma.toString());
                if (Parameters.string.TIPO_FIRMA_MULTIPLE.equals(archivoFirma.getTipoFirma())) {
                    // cantidadFirmas=1, cantidadFirmado=0
                    boolean s = archivoFirma.getCantidadFirmado() < archivoFirma.getCantidadFirmas();
                    if (s && !archivoFirma.getEmailFirmantes().toLowerCase()
                            .contains(usuario.getCorreoElectronico().toLowerCase())) {
                        archivoFirma.setEstado(Parameters.string.ESTADO_ARCHIVO_PENDIENTE_FIRMA);
                        archivoFirma
                                .setResultadoFirma("Este archivo aún está en proceso de firmado por otras personas");
                        throw new FirmaException("Este archivo aún está en proceso de firmado por otras personas");
                    }
                }
                propietario = (archivoFirma.getIdUsuario() == usuario.getIdUsuario());
                servicioService.validarServicio(archivoFirma.getIdUsuario(), propietario);
                String path = archivoFirma.getRutaRelativaArchivo();
                String pathOriginalTemporal = getRutaArchivos(true) + path + "" + archivoFirma.getNombreArchivo();
                File temporal = new File(pathOriginalTemporal);
                if (temporal.exists()) {
                    FirmaArchivoUsuario firma = firmaArchivoService.guardarFirmaArchivoUsuario(archivoFirma, usuario,
                            propietario, archivoFirma.getHashArchivo(), "", "", ipAddress, userAgent);
                    logger.info("Ruta de Arhivo original: " + pathOriginalTemporal);

                    List<InputStream> streams = new ArrayList<InputStream>();
                    streams.add(new FileInputStream(pathOriginalTemporal));
                    ArchivoFirma archivoFirmaTmp = archivoFirmaDao.findById(archivoFirma.getIdArchivoFirma());
                    String fileFirmantes = new GeneratePDF().createPDF(ArchivoFirmaHelper.getArchivoFirmaHandle(archivoFirmaTmp), env.getProperty("routes.custom.queryfile"));
                    streams.add(new FileInputStream(fileFirmantes));
                    String rutaPrincipalDestino = getRutaArchivos(false);
                    Utilities.crearDirectorio(rutaPrincipalDestino + path, true);
                    String fileName = "SIGNED_" + archivoFirma.getNombreArchivo();
                    String rutaArhivoDestino = rutaPrincipalDestino + path + fileName;
                    rutaArhivoDestino = rutaArhivoDestino.replace("original/", ""); // Cambiar File.separator por "/"
                    logger.info("Ruta de Arhivo Destino " + rutaArhivoDestino);
                    OutputStream outputStream = new FileOutputStream(rutaArhivoDestino);
                    GeneratePDF.mergePDF(streams, outputStream);
                    String hash = Utilities.generarHash(rutaArhivoDestino);
                    logger.info("Hash  " + hash);
                    // guardar firma en db
                    firmaArchivoService.actualizarFirma(hash, path.replace("original" + File.separator, ""), fileName,
                            firma.getIdFirmaArchivoUsuario());
                    desactivarProcesoFirmaBySMSIdUSuario(csms, usuario.getIdUsuario());

                    logger.info("Código de proceso desactivado");
                    int firmas = archivoFirma.getCantidadFirmado() + 1;
                    archivoFirmaDao.actualizarCantidadFirmas(firmas, archivoFirma.getIdArchivoFirma());
                    if (Parameters.string.TIPO_FIRMA_SINGLE.equals(archivoFirma.getTipoFirma())
                            || firmas > archivoFirma.getCantidadFirmas()) {
                        archivoFirmaDao.actualizarEstadoArchivoFirma(archivoFirma.getIdArchivoFirma(),
                                Parameters.string.ESTADO_ARCHIVO_FIRMADO);
                        Utilities.isBorrarArchivo(pathOriginalTemporal);
                        archivoFirma.setEstado(Parameters.string.ESTADO_ARCHIVO_FIRMADO);
                    } else if (Parameters.string.TIPO_FIRMA_OTHERS.equals(archivoFirma.getTipoFirma())) {
                        boolean completo = false;
                        String[] array = archivoFirma.getEmailFirmantes().split(",");
                        for (String email : array) {
                            logger.info("Tipo de firma otros " + email + " ID Archivo "
                                    + firma.getIdArchivoFirma().getIdArchivoFirma());
                            Optional<FirmaArchivoUsuario> firmantes = firmaArchivoService.consultarFirmaDocumento(email,
                                    firma.getIdArchivoFirma().getIdArchivoFirma());
                            completo = firmantes.isPresent();
                        }
                        if (completo) {
                            logger.info("Archivo firmado por otros correctamente");
                            archivoFirmaDao.actualizarEstadoArchivoFirma(archivoFirma.getIdArchivoFirma(),
                                    Parameters.string.ESTADO_ARCHIVO_FIRMADO);
                            Utilities.isBorrarArchivo(pathOriginalTemporal);
                            archivoFirma.setEstado(Parameters.string.ESTADO_ARCHIVO_FIRMADO);
                        } else {
                            archivoFirma.setEstado(Parameters.string.ESTADO_ARCHIVO_PENDIENTE_FIRMA);
                        }
                    } else {
                        archivoFirma.setEstado(Parameters.string.ESTADO_ARCHIVO_PENDIENTE_FIRMA);
                    }
                    archivoFirma.setResultadoFirma("OK");

                }
            } catch (FirmaException | LazyInitializationException | IOException | ParseException | WriterException e) {
                logger.error(e.getMessage());
                archivoFirma
                        .setResultadoFirma(archivoFirma.getResultadoFirma() == null ? "No se pudo firmar el documento"
                                : archivoFirma.getResultadoFirma());
                servicioService.retornarBolsa(archivoFirma.getIdUsuario(), propietario);
            } catch (ServicioFirmeseException e) {
                logger.error(e.getMessage());
                archivoFirma.setResultadoFirma(e.getMessage());
            }
        }
        return true;
    }

    private List<String> extraerArchivosDelToken(String token) {
        List<String> idsArchivos = new ArrayList<>();
        
        if (token == null || token.trim().isEmpty()) {
            return idsArchivos;
        }
        
        try {
            // Buscar el token en la base de datos para obtener los IDs de archivos
            Token tokenEntity = tokenService.findTokenByID(token);
            if (tokenEntity != null && tokenEntity.getIds() != null) {
                String ids = tokenEntity.getIds();
                // Los IDs vienen en formato "0-ID1-ID2-ID3-0"
                String[] partes = ids.split("-");
                for (String parte : partes) {
                    if (!parte.equals("0") && !parte.trim().isEmpty()) {
                        try {
                            Long.parseLong(parte); // Validar que sea un número
                            idsArchivos.add(parte);
                        } catch (NumberFormatException e) {
                            logger.warn("ID de archivo no válido: " + parte);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error al extraer archivos del token: " + token, e);
        }
        
        return idsArchivos;
    }

    private List<ArchivoFirmaResponseDTO> getListaResponse(List<ArchivoFirma> lista, Usuario usuario) {
        logger.info("Entrando a método : getListaResponse");
        if (lista != null && !lista.isEmpty()) {
            List<ArchivoFirmaResponseDTO> archivos = ArchivoFirmaHelper.convertList(lista);
            for (ArchivoFirmaResponseDTO dto : archivos) {
                if ("OK".equals(dto.getResultadoFirma())) {
                    if (dto.getFirmas() != null && !dto.getFirmas().isEmpty()) {
                        dto.setFechaRegistroStr(dto.getFirmas().get(0).getFechaFirmaStr());
                        dto.setIp(dto.getFirmas().get(0).getIpFirma());
                    }
                } else {
                    dto.setFechaRegistroStr("...");
                    dto.setIp("...");
                }
            }
            notificarFirma(lista, usuario);
            return archivos;
        } else {
            return new ArrayList<>();
        }
    }

    private void notificarFirma(List<ArchivoFirma> lista, Usuario user) {
        try {
            logger.info("🔔 Iniciando validación de notificación de firma");

            // Filtrar archivos que NO son TyC para la notificación
            List<ArchivoFirma> archivosParaNotificar = filtrarArchivosNoTyC(lista);

            // Si no hay archivos reales para notificar, no enviar notificación
            if (archivosParaNotificar.isEmpty()) {
                logger.info("📋 No hay archivos reales para notificar (solo TyC firmados). Omitiendo notificación.");
                return;
            }

            Set<String> emails = new HashSet<>();
            boolean hayArchivosFirmados = false;
            long idUsuario = 0;
            List<String[]> adjuntos = new ArrayList<>();

            if (lista != null && !lista.isEmpty()) {
                Optional<Usuario> usuario = iUsuarioDao.findById(lista.get(0).getIdUsuario());
                if (usuario.isPresent()) {
                    logger.info("👤 Usuario encontrado: " + usuario.get().getCorreoElectronico());
                    idUsuario = usuario.get().getIdUsuario();
                    emails.add(usuario.get().getCorreoElectronico());

                    // Procesar archivos para notificación y adjuntos
                    for (ArchivoFirma archivoFirma : archivosParaNotificar) {
                        logger.info("📧 Procesando emails firmantes: " + archivoFirma.getEmailFirmantes());

                        // Recopilar emails de firmantes
                        if (archivoFirma.getEmailFirmantes() != null && !archivoFirma.getEmailFirmantes().trim().isEmpty()) {
                            Arrays.stream(archivoFirma.getEmailFirmantes().split(","))
                                  .forEach(email -> emails.add(email.trim()));
                        }

                        // Preparar adjuntos de archivos firmados
                        if (archivoFirma.getEstado() == Parameters.string.ESTADO_ARCHIVO_FIRMADO) {
                            String[] infoArchivo = {
                                env.getProperty("routes.custom.file") + File.separator + archivoFirma.getRutaArchivoFirmado(),
                                "SIGNED_" + archivoFirma.getNombreArchivo()
                            };
                            hayArchivosFirmados = true;
                            adjuntos.add(infoArchivo);
                        }
                    }
                }
            }

            // Generar contenido del email usando template centralizado
            String contenidoEmail = generarContenidoNotificacionFirma(archivosParaNotificar);
            ServicioDTO servicioDTO = servicioService.findById(idUsuario);

            boolean debeNotificar = hayArchivosFirmados && servicioDTO.isNotificarFirma();

            List<FirmaRequestDTO> firmas = new ArrayList<>();
            logger.info("📬 Se notifica a firmantes :: " + debeNotificar);

            if (debeNotificar) {
                // Enviar notificación por email usando template centralizado
                emails.forEach(email -> {
                    try {
                        EmailService.enviarCorreo(email, "Proceso de firma completo", contenidoEmail, adjuntos);
                        logger.info("✅ Email enviado a: " + email);
                    } catch (Exception ex) {
                        logger.error("❌ Error enviando email a " + email + ": " + ex.getMessage());
                        throw new RuntimeException(ex);
                    }
                });

                // Preparar datos para notificación de endpoint (solo archivos reales)
                for (ArchivoFirma archivo : archivosParaNotificar) {
                    FirmaRequestDTO dto = new FirmaRequestDTO();
                    dto.setIdArchivo(archivo.getIdArchivoFirma());
                    dto.setNombreArchivo(archivo.getNombreArchivo());
                    dto.setEstado(archivo.getEstado() == Parameters.string.ESTADO_ARCHIVO_FIRMADO ? "FIRMADO" : "NO FIRMADO");
                    firmas.add(dto);
                }

                Utilities.notificarEndpoint(servicioDTO.isEndpointCBackHabilitado(), servicioDTO.getEndpointCBack(), firmas);

            } else {
                logger.info("🔍 Validación de firmantes para documentos múltiples");
                ArchivoFirma archivoFirma = lista.get(0);
                if (Parameters.string.TIPO_FIRMA_MULTIPLE.equals(archivoFirma.getTipoFirma())) {
                    int firmasRestantes = archivoFirma.getCantidadFirmas() - archivoFirma.getCantidadFirmado();
                    logger.info("📝 Documento múltiple firma con " + firmasRestantes + " firmas restantes");

                    if (firmasRestantes == 1) {
                        logger.info("👤 Persona que firma: " + user.getNombreCompleto());
                        List<FirmaArchivoUsuario> firmasArchivos = firmaArchivoService.findByIdArchivoFirmaUsuarioPropietario(
                                archivoFirma.getIdArchivoFirma(), user.getIdUsuario());
                        logger.info("🔍 Consulta firmantes realizada: " + firmasArchivos);

                        if (firmasArchivos == null || firmasArchivos.isEmpty()) {
                            logger.info("⏳ El propietario no ha firmado");
                            FirmaRequestDTO dto = new FirmaRequestDTO();
                            dto.setIdArchivo(archivoFirma.getIdArchivoFirma());
                            dto.setNombreArchivo(archivoFirma.getNombreArchivo());
                            dto.setEstado("Pendiente firma de representante legal");
                            firmas.add(dto);
                            Utilities.notificarEndpoint(servicioDTO.isEndpointCBackHabilitado(), servicioDTO.getEndpointCBack(), firmas);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("❌ Error al notificar firma: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private String getRutaArchivos(boolean temporal) {
        String rutaBase = env.getProperty("routes.custom.file", "/opt/firmese/files");
        if (temporal) {
            return rutaBase + "/tmp/";
        } else {
            return rutaBase + "/";
        }
    }

    public ProcesoFirma buscarUltimoEnvio(String id) throws FirmaException {
        try {
            long idUsuario = Long.parseLong(id);
            ProcesoFirma proceso = dao.findValidOtpByUsuario(idUsuario);
            
            if (proceso != null) {
                logger.info("Proceso OTP válido encontrado - ID: {}, Código: {}, Vencimiento: {}", 
                           proceso.getIdProcesoFirma(), proceso.getCodigoSms(), proceso.getFechaVencimiento());
                return proceso;
            }
            
            logger.warn("No se encontró proceso OTP válido para usuario: {}", id);
            throw new FirmaException("No se encontraron códigos OTP válidos");
        } catch (NumberFormatException e) {
            logger.error("ID de usuario no válido: {}", id);
            throw new FirmaException("ID de usuario no válido");
        } catch (Exception e) {
            logger.error("Error al buscar proceso OTP para usuario {}: {}", id, e.getMessage());
            throw new FirmaException(e.getMessage());
        }
    }

    public List<ProcesoFirma> findAllByIdUsuario(Usuario usuario) {
        return dao.findAllByIdUsuario(usuario);
    }

    private void completarFirmado(ArchivoFirma archivoFirma, String rutaRelativa, String pathOriginalTemporal, boolean eliminaArchivo) throws FirmaException, IOException {
        if (archivoFirma.isComplete()) {
            logger.info("Se ha completado el proceso de firma tipo {}", archivoFirma.getTipoFirma());
            archivoFirma.setEstado(Parameters.string.ESTADO_ARCHIVO_FIRMADO);
            archivoFirmaDao.actualizarEstadoArchivoFirma(archivoFirma.getIdArchivoFirma(), archivoFirma.getEstado());
            generarDocumentoFirmado(rutaRelativa, pathOriginalTemporal, archivoFirma.getIdArchivoFirma(), archivoFirma.getIdUsuario());
            if (eliminaArchivo) {
                Utilities.isBorrarArchivo(pathOriginalTemporal);
            }
            verificarYActivarSiguienteOrden(archivoFirma.getIdArchivoFirma(), obtenerOrdenFirmadoActual(archivoFirma), archivoFirma.getTipoFirma());
        } else {
            archivoFirma.setEstado(Parameters.string.ESTADO_ARCHIVO_PENDIENTE_FIRMA);
        }
        archivoFirma.setResultadoFirma("OK");

    }

    /**
     * Obtiene el orden de firma actual para el archivo.
     * Si no se puede determinar, retorna 1 por defecto.
     */
    private int obtenerOrdenFirmadoActual(ArchivoFirma archivoFirma) {
        try {
            // Busca la última firma realizada para este archivo
            List<FirmaArchivoUsuario> firmas = firmaArchivoService.findByIdArchivoFirma(archivoFirma.getIdArchivoFirma());
            if (firmas != null && !firmas.isEmpty()) {
                FirmaArchivoUsuario ultimaFirma = firmas.get(firmas.size() - 1);
                SolicitudFirma solicitud = solicitudFirmaService.findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(
                        archivoFirma.getIdArchivoFirma(), ultimaFirma.getIdUsuario().getCorreoElectronico());
                if (solicitud != null) {
                    return solicitud.getOrdenFirma();
                }
            }
        } catch (Exception e) {
            logger.warn("No se pudo obtener el orden firmado actual: " + e.getMessage());
        }
        // Valor por defecto si no se encuentra
        return 1;
    }

    public ArchivoFirma confirmarFirmado(Integer idArchivo) throws FirmaException {
        try {
            ArchivoFirma archivoFirma = archivoFirmaDao.findById(idArchivo);
            if (archivoFirma != null) {
                String rutaRelativa = archivoFirma.getRutaRelativaArchivo();
                String pathOriginalTemporal = getRutaArchivos(true) + rutaRelativa + "" + archivoFirma.getNombreArchivo();
                File temporal = new File(pathOriginalTemporal);
                if (temporal.exists() && archivoFirma.isComplete()) {
                    // 1. Actualizar solicitudes como firmadas ANTES de completar firmado
                    List<FirmaArchivoUsuario> firmados = firmaArchivoService.firmadosSolicitudPendiente(idArchivo);
                    for (FirmaArchivoUsuario firmado : firmados) {
                        SolicitudFirma solicitudFirma = solicitudFirmaService.findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(archivoFirma.getIdArchivoFirma(), firmado.getIdUsuario().getCorreoElectronico());
                        if (solicitudFirma != null) {
                            solicitudFirma.setFirmado(true);
                            solicitudFirma.setFechaFirma(firmado.getFechaRegistro());
                            solicitudFirmaService.save(solicitudFirma);
                        }
                    }
                    // 2. Ahora llamar a completarFirmado (que activa el siguiente orden si corresponde)
                    completarFirmado(archivoFirma, rutaRelativa, pathOriginalTemporal, false);
                }
                return archivoFirma;
            }
            throw new FirmaException("No se encontro archivo con id " + idArchivo);
        } catch (IOException e) {
            throw new FirmaException("Error :: " + e.getMessage());
        }
    }

    /**
     * Verifica si se firmó una plantilla de TyC y actualiza el estado del usuario - BASADO EN NOMBRES Y FALLBACK
     */
    private void verificarYActualizarTyC(String token, String emailFirmante) {
        try {
            logger.info("🔍 Verificando TyC - Token: " + (token != null ? "Disponible" : "null"));
            
            // **AGREGAR VALIDACIÓN PARA TOKEN NULL**
            if (token == null || token.trim().isEmpty()) {
                logger.info("🔍 Token null - Verificando TyC por archivos firmados recientemente...");
                verificarTyCPorArchivosFirmados(emailFirmante);
                return;
            }
            
            // **CÓDIGO ORIGINAL PARA CUANDO SÍ HAY TOKEN**
            logger.info("🔍 Verificando si el token corresponde a plantilla TyC");
            
            Token tokenEntity = tokenService.consultarToken(token);
            if (tokenEntity != null) {
                String ids = tokenEntity.getIds();
                logger.info("IDs del token: " + ids);
                
                if (ids != null && ids.contains("-")) {
                    String[] partes = ids.split("-");
                    
                    for (String parte : partes) {
                        try {
                            if (!parte.equals("0")) {
                                Long idArchivo = Long.parseLong(parte);
                                
                                ArchivoFirma archivoFirma = archivoFirmaDao.findById(idArchivo);
                                if (archivoFirma != null) {
                                    String nombreArchivo = archivoFirma.getNombreArchivo();
                                    
                                    logger.info("🔍 Verificando archivo: " + nombreArchivo);
                                    
                                    // **VERIFICACIÓN POR NOMBRE** en lugar de ID
                                    if (esArchivoTyCPorNombre(nombreArchivo)) {
                                        logger.info("🎯 DETECTADA FIRMA DE TyC:");
                                        logger.info("   - Archivo: " + nombreArchivo);
                                        logger.info("   - Email: " + emailFirmante);
                                        logger.info("   - ID Archivo: " + idArchivo);
                                        
                                        // Marcar TyC como firmados
                                        terminosCondicionesService.marcarTyCFirmadosPorEmail(emailFirmante);
                                        
                                        logger.info("✅ Proceso TyC completado para: " + emailFirmante);
                                        break;
                                    }
                                }
                            }
                        } catch (NumberFormatException e) {
                            continue;
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            logger.info("Error verificando TyC (no crítico): " + e.getMessage());
        }
    }

    /**
     * NUEVO MÉTODO: Verifica TyC basándose en archivos recientemente firmados por el usuario
     */
    private void verificarTyCPorArchivosFirmados(String emailFirmante) {
        try {
            logger.info("🔍 Verificando TyC por archivos firmados para: " + emailFirmante);
            
            // 1. Buscar usuario por email
            Optional<Usuario> usuarioOpt = iUsuarioDao.findByEmail(emailFirmante);
            if (!usuarioOpt.isPresent()) {
                logger.info("❌ Usuario no encontrado: " + emailFirmante);
                return;
            }
            
            Usuario usuario = usuarioOpt.get();
            logger.info("👤 Usuario encontrado: " + usuario.getNombreCompleto() + " (ID: " + usuario.getIdUsuario() + ")");
            
            // 2. Obtener archivos firmados recientemente (últimos 5 minutos)
            List<ArchivoFirma> archivosRecientes = archivoFirmaDao.findRecentlySignedByUser(usuario.getIdUsuario());
            
            logger.info("📄 Archivos firmados recientemente: " + archivosRecientes.size());
            
            // 3. Verificar si alguno es TyC
            boolean encontradoTyC = false;
            for (ArchivoFirma archivo : archivosRecientes) {
                String nombreArchivo = archivo.getNombreArchivo();
                logger.info("🔍 Verificando archivo reciente: " + nombreArchivo);
                
                if (esArchivoTyCPorNombre(nombreArchivo)) {
                    logger.info("🎯 DETECTADA FIRMA DE TyC por archivo:");
                    logger.info("   - Archivo: " + nombreArchivo);
                    logger.info("   - Email: " + emailFirmante);
                    logger.info("   - ID Archivo: " + archivo.getIdArchivoFirma());
                    
                    terminosCondicionesService.marcarTyCFirmadosPorEmail(emailFirmante);
                    logger.info("✅ Proceso TyC completado por archivo para: " + emailFirmante);
                    encontradoTyC = true;
                    break;
                }
            }
            
            if (!encontradoTyC) {
                logger.info("ℹ️ No se encontraron archivos TyC en firmas recientes");
            }
            
        } catch (Exception e) {
            logger.warn("Error verificando TyC por archivos firmados: " + e.getMessage());
        }
    }

    /**
     * Determina si el nombre del archivo corresponde a un archivo de Términos y Condiciones (TyC).
     */
    private boolean esArchivoTyCPorNombre(String nombreArchivo) {
        if (nombreArchivo == null || nombreArchivo.trim().isEmpty()) {
            return false;
        }

        String nombre = nombreArchivo.toLowerCase().trim();

        // Patrones específicos para los archivos TyC oficiales
        boolean esTyC =
            // 1. Términos y Condiciones
            nombre.contains("términos y condiciones") ||
            nombre.contains("terminos y condiciones") ||
            nombre.contains("tyc") ||

            // 2. Autorización de Datos Personales
            nombre.contains("autorización datos personales") ||
            nombre.contains("autorizacion datos personales") ||
            nombre.contains("datos personales") ||

            // 3. Patrones más específicos para los archivos exactos
            nombre.contains("2025 - términos y condiciones") ||
            nombre.contains("2025. autorización datos personales") ||

            // 4. Variaciones sin acentos
            nombre.contains("terminos") && nombre.contains("condiciones") ||
            nombre.contains("autorizacion") && nombre.contains("personales");

        if (esTyC) {
            logger.info("✅ Archivo TyC detectado: " + nombreArchivo);
        } else {
            logger.debug("ℹ️ Archivo NO es TyC: " + nombreArchivo);
        }

        return esTyC;
    }

    /**
     * Filtra la lista de archivos excluyendo los archivos de Términos y Condiciones (TyC)
     * para que no aparezcan en las notificaciones a los usuarios.
     *
     * @param archivos Lista completa de archivos
     * @return Lista filtrada sin archivos TyC
     */
    private List<ArchivoFirma> filtrarArchivosNoTyC(List<ArchivoFirma> archivos) {
        if (archivos == null || archivos.isEmpty()) {
            return new ArrayList<>();
        }

        List<ArchivoFirma> archivosNoTyC = new ArrayList<>();

        for (ArchivoFirma archivo : archivos) {
            if (!esArchivoTyCPorNombre(archivo.getNombreArchivo())) {
                archivosNoTyC.add(archivo);
                logger.debug("📄 Archivo incluido en notificación: " + archivo.getNombreArchivo());
            } else {
                logger.info("🚫 Archivo TyC excluido de notificación: " + archivo.getNombreArchivo());
            }
        }

        logger.info("📊 Archivos filtrados: " + archivosNoTyC.size() + " de " + archivos.size() + " archivos totales");
        return archivosNoTyC;
    }

    /**
     * Genera el contenido HTML para la notificación de firma usando el template centralizado
     *
     * @param archivos Lista de archivos para incluir en la notificación
     * @return Contenido HTML del email
     */
    private String generarContenidoNotificacionFirma(List<ArchivoFirma> archivos) {
        List<EmailTemplateService.ArchivoNotificacionDTO> archivosParaTemplate = new ArrayList<>();

        for (ArchivoFirma archivo : archivos) {
            String estadoTexto = archivo.getEstado() == Parameters.string.ESTADO_ARCHIVO_FIRMADO ? "FIRMADO" : "NO FIRMADO";
            archivosParaTemplate.add(new EmailTemplateService.ArchivoNotificacionDTO(archivo.getNombreArchivo(), estadoTexto));
        }

        return EmailTemplateService.generarTemplateNotificacionProcesoCompletado(archivosParaTemplate);
    }
}
