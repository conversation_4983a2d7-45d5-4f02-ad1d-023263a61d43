package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.PaqueteServicio;
import se.firme.ms.datos.models.entity.Usuario;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(Servicio.class)
public class Servicio_ { 

    public static volatile SingularAttribute<Servicio, String> endpointCBack;
    public static volatile SingularAttribute<Servicio, Boolean> endpointCBackHabilitado;
    public static volatile SingularAttribute<Servicio, String> tipoServicio;
    public static volatile SingularAttribute<Servicio, Date> fechaVencimiento;
    public static volatile ListAttribute<Servicio, PaqueteServicio> paqueteServicioList;
    public static volatile SingularAttribute<Servicio, Boolean> notificarFirma;
    public static volatile SingularAttribute<Servicio, String> tipoValidacion;
    public static volatile SingularAttribute<Servicio, Integer> cantOtros;
    public static volatile SingularAttribute<Servicio, Date> fechaActualizacion;
    public static volatile SingularAttribute<Servicio, Usuario> usuario;
    public static volatile SingularAttribute<Servicio, Long> idServicio;
    public static volatile SingularAttribute<Servicio, Integer> cantidadFirmas;
    public static volatile SingularAttribute<Servicio, Boolean> orisHabilitado;

}