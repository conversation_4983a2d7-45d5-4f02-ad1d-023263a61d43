package se.firme.ms.models.service.helper;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import se.firme.commons.exception.FirmaException;
import se.firme.ms.utils.EmailNotificationValidationUtils;
import se.firme.commons.firmese.dto.DatosEmailMultipleFirmaDTO;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.TokenServiceImpl;
import se.firme.ms.models.service.interfaz.IArchivoFirmaService;
import se.firme.ms.models.service.interfaz.IUsuarioService;
import se.firme.ms.datos.models.entity.Token;

/**
 * Helper para la extracción y preparación de datos de email para notificaciones de firma.
 *
 * Centraliza la lógica de obtención de datos necesarios para el envío de notificaciones
 * por email en los procesos de firma.
 *
 */
@Component
public class EmailDataHelper {

    private static final Logger logger = LoggerFactory.getLogger(EmailDataHelper.class);

    private final IUsuarioService usuarioService;
    private final TokenServiceImpl tokenServiceImpl;
    private final IArchivoFirmaService archivoFirmaService;

    /**
     * Constructor para inyección de dependencias.
     *
     * @param usuarioService Servicio para operaciones con usuarios
     * @param tokenServiceImpl Servicio para operaciones con tokens
     * @param archivoFirmaService Servicio para operaciones con archivos de firma
     */
    public EmailDataHelper(IUsuarioService usuarioService,
                          TokenServiceImpl tokenServiceImpl,
                          IArchivoFirmaService archivoFirmaService) {
        this.usuarioService = usuarioService;
        this.tokenServiceImpl = tokenServiceImpl;
        this.archivoFirmaService = archivoFirmaService;
    }
    
    /**
     * Extrae datos necesarios para el envío de notificaciones de firma múltiple.
     *
     * USADO EN: TokenGenerationService, FirmaOrdenService y firmaNegocio
     *
     * @param idUsuario ID del usuario remitente
     * @param emailFirmante Email del firmante destinatario
     * @param nombreFirmante Nombre del firmante (opcional, se busca si no se proporciona)
     * @param nombresDocumentos Lista de nombres de documentos a firmar
     * @return DTO con todos los datos necesarios para el email
     * @throws FirmaException Si hay errores de validación o datos faltantes
     */
    public DatosEmailMultipleFirmaDTO extraerDatosNotificacionCorreo(Long idUsuario, String emailFirmante,
            String nombreFirmante, List<String> nombresDocumentos) throws FirmaException {

        logger.info("Iniciando extracción de datos para envío del correo - Usuario: {}, Firmante: {}", idUsuario, emailFirmante);

        try {
            // 1. VALIDAR PARÁMETROS DE ENTRADA
            EmailNotificationValidationUtils.validarIdUsuario(idUsuario);
            EmailNotificationValidationUtils.validarEmailFirmante(emailFirmante);

            // 2. OBTENER Y VALIDAR DATOS DEL REMITENTE
            Usuario remitente = usuarioService.findById(idUsuario);
            EmailNotificationValidationUtils.validarUsuarioRemitente(remitente, idUsuario);

            String emailRemitente = remitente.getCorreoElectronico();
            String nombreRemitente = remitente.getNombreCompleto();

            // 2. OBTENER NOMBRE DEL FIRMANTE (usar proporcionado o buscar por email)
            String nombreFirmanteReal = nombreFirmante != null && !nombreFirmante.trim().isEmpty() ?
                    nombreFirmante : obtenerNombreFirmante(emailFirmante);

            // 3. CREAR Y CONFIGURAR DTO CON TODOS LOS DATOS
            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos);
            datos.setNombreFirmante(nombreFirmanteReal);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);
            
            logger.info("Datos extraídos exitosamente - Remitente: {}, Firmante: {}, Documentos: {}",
                       nombreRemitente, nombreFirmanteReal, nombresDocumentos.size());

            return datos;

        } catch (FirmaException e) {
            // Re-lanzar excepciones de validación con logging apropiado
            logger.error("Error de validación en extracción de datos para notificación de firma: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            // Capturar errores técnicos inesperados
            logger.error("Error técnico inesperado obteniendo datos del firmante", e);
            throw new FirmaException("Error técnico obteniendo datos del firmante: " + e.getMessage());
        }
    }

    /**
     * Extrae datos para reenvío de notificaciones al correo basándose en un token existente.
     *
     * USADO EN: FirmaNegocio.reenviarSolicitudFirma
     *
     * @param token Token con la información de la solicitud de firma
     * @return DTO con datos completos para el email
     * @throws FirmaException Si el token es inválido o faltan datos
     */
    public DatosEmailMultipleFirmaDTO extraerDatosNotificacionCorreoDesdeToken(Token token) throws FirmaException {

        logger.info("Extrayendo datos desde token - ID: {}", token != null ? token.getIdToken() : "null");

        try {
            // 1. VALIDAR TOKEN Y OBTENER DATOS DEL REMITENTE
            EmailNotificationValidationUtils.validarToken(token);

            Usuario remitente = token.getIdUsuario();
            EmailNotificationValidationUtils.validarUsuarioCompleto(remitente, "Usuario remitente del token");

            String nombreRemitente = remitente.getNombreCompleto();
            String emailRemitente = remitente.getCorreoElectronico();

            // 2. OBTENER NOMBRE DEL FIRMANTE (desde token.getEmailFirmante())
            String nombreFirmante = obtenerNombreFirmante(token.getEmailFirmante());

            // 3. OBTENER NOMBRES REALES DE DOCUMENTOS (desde token.getIds())
            List<String> nombresDocumentos = obtenerNombresRealesDocumentos(token.getIds());

            // 4. CREAR DTO COMPLETO
            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos);
            datos.setNombreFirmante(nombreFirmante);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);

            logger.info("Datos para notificación de firma extraídos desde token exitosamente: Remitente: {} ({})", nombreRemitente, emailRemitente);

            return datos;

        } catch (FirmaException e) {
            // Re-lanzar excepciones específicas de validación
            logger.warn("Error de validación procesando token: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            // Capturar errores técnicos inesperados
            logger.error("Error técnico inesperado procesando token para notificación de firma: {}", e);
            throw new FirmaException("Error técnico procesando token: " + e.getMessage());
        }
    }

    /**
     * Obtiene el nombre del firmante por su email.
     *
     * @param email Email del firmante
     * @return Nombre completo del firmante
     * @throws FirmaException Si no se encuentra o no tiene nombre
     */
    private String obtenerNombreFirmante(String email) throws FirmaException {
        // Validar email de entrada
        EmailNotificationValidationUtils.validarEmailFirmante(email);

        try {
            Usuario firmante = usuarioService.findByEmail(email);

            // Validar usuario firmante completo
            EmailNotificationValidationUtils.validarUsuarioFirmante(firmante, email);

            return firmante.getNombreCompleto();

        } catch (FirmaException e) {
            // Re-lanzar excepciones de validación
            throw e;
        } catch (Exception e) {
            // Capturar errores técnicos inesperados
            logger.error("Error técnico obteniendo nombre firmante para email: {}", email, e);
            throw new FirmaException("Error crítico en la obtención de datos del nombre del firmante: " + e.getMessage());
        }
    }
    /**
     * Obtiene los nombres reales de los documentos a partir de un string de IDs.
     *
     * Procesa el formato especial de IDs (ej: "0-123-456-0") y obtiene los nombres
     * reales de los archivos desde la base de datos.
     *
     * @param ids String con IDs de documentos en formato especial
     * @return Lista de nombres reales de los documentos
     * @throws FirmaException Si no se pueden obtener los nombres
     */
    private List<String> obtenerNombresRealesDocumentos(String ids) throws FirmaException {
        // Validar IDs de entrada
        EmailNotificationValidationUtils.validarIdsDocumentos(ids);

        try {
            // Limpiar formato especial: quitar "0-" del inicio y "-0" del final
            String idsLimpios = ids;
            if (idsLimpios.startsWith("0-")) {
                idsLimpios = idsLimpios.substring(2);
            }
            if (idsLimpios.endsWith("-0")) {
                idsLimpios = idsLimpios.substring(0, idsLimpios.length() - 2);
            }

            // Usar el servicio de archivoFirma para encontrar los nombres
            List<ArchivoFirma> archivos = archivoFirmaService.buscarArchivosFirma(idsLimpios);

            // Extraer nombres válidos
            List<String> nombresReales = archivos.stream()
                    .map(ArchivoFirma::getNombreArchivo)
                    .filter(nombre -> nombre != null && !nombre.trim().isEmpty())
                    .collect(Collectors.toList());

            if (nombresReales.isEmpty()) {
                logger.error("No se encontraron nombres válidos para IDs: {}", idsLimpios);
                throw new FirmaException("Archivos encontrados pero sin nombres válidos para IDs: " + idsLimpios);
            }

            return nombresReales;

        } catch (FirmaException e) {
            // Re-lanzar excepciones de validación
            throw e;
        } catch (Exception e) {
            // Capturar errores técnicos inesperados
            logger.error("Error técnico obteniendo nombres de archivos para IDs: {}", ids, e);
            throw new FirmaException("Error crítico al obtener nombres de los archivos: " + e.getMessage());
        }
    }

    /**
     * Extrae datos para notificación de solicitud con un adjunto o sin adjunto como fallback.
     *
     * USADO EN: EmailNotificationService.enviarNotificacionSolicitud y enviarNotificacionSolicitudSinAdjuntos
     *
     * @param email Email del firmante
     * @param nombreArchivo Nombre del archivo (opcional)
     * @param tokenString String del token para obtener datos del remitente
     * @return DTO con datos para notificación
     * @throws FirmaException Si faltan datos requeridos o hay errores de validación
     */
    public DatosEmailMultipleFirmaDTO extraerDatosNotificacionCorreoSimple(String email, String nombreArchivo,
                                                           String tokenString) throws FirmaException {

        logger.info("Extrayendo datos para notificación simple al correo - Email: {}, Token: {}", email, tokenString);

        try {
            // 1. VALIDAR PARÁMETROS DE ENTRADA
            EmailNotificationValidationUtils.validarEmailFirmante(email);
            EmailNotificationValidationUtils.validarTokenString(tokenString);

            // 2. PREPARAR LISTA DE DOCUMENTOS 
            List<String> nombresDocumentos = nombreArchivo != null && !nombreArchivo.trim().isEmpty() ?
                Arrays.asList(nombreArchivo) : null;

            // 3. OBTENER NOMBRE DEL FIRMANTE
            String nombreFirmante = obtenerNombreFirmante(email);

            // 4. OBTENER Y VALIDAR TOKEN Y REMITENTE
            Token tokenEntity = tokenServiceImpl.findTokenByID(tokenString);
            EmailNotificationValidationUtils.validarToken(tokenEntity);

            Usuario remitente = tokenEntity.getIdUsuario();
            EmailNotificationValidationUtils.validarUsuarioCompleto(remitente, "Usuario remitente del token");

            String nombreRemitente = remitente.getNombreCompleto();
            String emailRemitente = remitente.getCorreoElectronico();

            // 4. CREAR DTO FINAL
            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos);
            datos.setNombreFirmante(nombreFirmante);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);

            logger.info("Datos para la notificación de firma con un adjunto extraídos exitosamente - Remitente: {}, Firmante: {}",
                       nombreRemitente, nombreFirmante);

            return datos;

        } catch (FirmaException e) {
            // Re-lanzar excepciones de validación
            logger.error("Error de validación en extracción : {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            // Capturar errores técnicos inesperados
            logger.error("Error técnico en extracción optimizada", e);
            throw new FirmaException("Error técnico al obtener datos desde string token: " + e.getMessage());
        }
    }

}