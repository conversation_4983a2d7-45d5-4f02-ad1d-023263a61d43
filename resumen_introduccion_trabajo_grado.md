# RESUMEN

Este informe final documenta los resultados obtenidos durante el desarrollo de las prácticas empresariales realizadas en VENKO S.A.S., una empresa colombiana especializada en consultoría TI y desarrollo de aplicaciones con 15 años de experiencia en el mercado. El trabajo se enfocó en el apoyo al desarrollo de software para la plataforma de firma electrónica "Firmese", un servicio integral de firma electrónica, recolección y validación de información personal con atributos de vigencia, veracidad, calidad y legalidad.

Durante el período de práctica, comprendido entre julio y diciembre de 2025, se ejecutaron actividades orientadas al desarrollo de funcionalidades tanto en el módulo backend desarrollado en Spring Boot como en el frontend implementado en NextJS. Las tareas incluyeron el desarrollo de endpoints REST para la gestión de usuarios, archivos, tokens y procesos de firma, así como la implementación de componentes de interfaz para las vistas de carga de documentos, selección de firmantes y seguimiento del estado del proceso de firma.

El proceso de desarrollo se estructuró bajo una metodología ágil basada en el marco de trabajo Scrum, lo cual permitió una planificación iterativa mediante Sprints y la validación progresiva del software a través de pruebas de calidad y pruebas de aceptación de usuario. Se utilizó la herramienta Planifique.se para la gestión de proyectos y seguimiento de actividades, facilitando la documentación detallada del progreso y la coordinación del equipo de desarrollo.

Las actividades desarrolladas contribuyeron significativamente al fortalecimiento de la plataforma Firmese, optimizando los procesos de autenticación, validación de usuarios y gestión de archivos. Se implementaron mejoras en los algoritmos de verificación y se desarrollaron soluciones que optimizaron el rendimiento y la experiencia del usuario final. Adicionalmente, se establecieron protocolos de pruebas para asegurar la calidad del código antes del despliegue, garantizando que las funcionalidades desarrolladas cumplieran con los requerimientos establecidos.

Este documento expone el contexto organizacional y técnico en el que se desarrollaron las actividades, así como el impacto de los resultados obtenidos en el entorno productivo. Finalmente, se destaca la evolución del proceso de aprendizaje y la consolidación de competencias profesionales en el área de ingeniería de sistemas e informática, particularmente en el desarrollo de aplicaciones empresariales con tecnologías modernas como Spring Boot y NextJS.

**Palabras clave:** Firmese, Firma electrónica, Spring Boot, NextJS, VENKO S.A.S., Desarrollo de software, Metodología Scrum, Autenticación digital, Gestión documental, Prácticas empresariales, Ingeniería de sistemas, API REST, Validación de usuarios, Experiencia profesional.

---

# INTRODUCCIÓN

Este informe final tiene como objetivo documentar el desarrollo completo de las prácticas empresariales realizadas en VENKO S.A.S., presentando los resultados alcanzados durante todo el proceso formativo, así como las experiencias obtenidas en el entorno productivo de una empresa especializada en consultoría TI y desarrollo de aplicaciones, con un enfoque particular en la evolución y fortalecimiento de su plataforma de firma electrónica "Firmese".

A lo largo de este documento, se detalla la experiencia adquirida en dicho entorno empresarial, enfocada en las tareas de desarrollo, mantenimiento y optimización de funcionalidades dentro del ecosistema tecnológico de Firmese. Se describe el impacto de las actividades desarrolladas tanto a nivel técnico como en el funcionamiento general de la plataforma, considerando su importancia en el contexto actual de la transformación digital y la necesidad creciente de soluciones de firma electrónica confiables y seguras.

La práctica empresarial se desarrolló en el marco de un proyecto integral que abarcó múltiples aspectos del ciclo de vida del desarrollo de software, desde la planificación y análisis de requerimientos hasta la implementación, pruebas y documentación de soluciones tecnológicas. Este enfoque holístico permitió una comprensión profunda de los procesos empresariales y la aplicación práctica de los conocimientos adquiridos durante la formación académica en Ingeniería de Sistemas e Informática.

Con el fin de contextualizar el rol desempeñado durante la práctica, se presenta inicialmente la situación organizacional de VENKO S.A.S., sus productos y servicios, así como la descripción detallada de la plataforma Firmese y su propuesta de valor en el mercado de soluciones de firma electrónica. Esto permite comprender el marco de trabajo, los desafíos técnicos enfrentados y los resultados obtenidos durante el proceso formativo.

El desarrollo de las actividades se enmarcó dentro de una metodología ágil basada en Scrum, lo que facilitó la adaptación a los cambios en los requerimientos y la entrega continua de valor al producto. Esta aproximación metodológica no solo contribuyó al éxito técnico del proyecto, sino que también proporcionó una valiosa experiencia en la gestión de proyectos de software en entornos empresariales reales.

La estructura de este documento refleja la progresión lógica del trabajo realizado, comenzando con la caracterización de la empresa y el contexto del proyecto, continuando con la descripción de la metodología empleada y los marcos conceptuales y tecnológicos utilizados, para finalmente presentar los resultados obtenidos y las conclusiones derivadas de esta experiencia formativa.

Es importante destacar que este trabajo no solo representa el cumplimiento de un requisito académico, sino que constituye una contribución real al desarrollo de una solución tecnológica que impacta positivamente en los procesos de digitalización y firma electrónica de múltiples organizaciones. La experiencia adquirida durante este período ha sido fundamental para la consolidación de competencias profesionales y la comprensión práctica de los desafíos y oportunidades presentes en el campo de la ingeniería de software aplicada a soluciones empresariales.

---

## Referencias

American Psychological Association. (2020). *Publication manual of the American Psychological Association* (7th ed.). American Psychological Association.

Ministerio de Tecnologías de la Información y las Comunicaciones. (2020). *Decreto 2364 de 2012 - Firma electrónica y digital*. Gobierno de Colombia.

Schwaber, K., & Sutherland, J. (2020). *The Scrum Guide: The definitive guide to Scrum: The rules of the game*. Scrum.org.

Spring Framework Team. (2024). *Spring Boot Reference Documentation*. Pivotal Software, Inc. https://spring.io/projects/spring-boot

Vercel Inc. (2024). *Next.js Documentation*. https://nextjs.org/docs
