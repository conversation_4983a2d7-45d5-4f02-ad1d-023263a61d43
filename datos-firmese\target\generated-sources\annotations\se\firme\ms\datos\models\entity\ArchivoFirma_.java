package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(ArchivoFirma.class)
public class ArchivoFirma_ { 

    public static volatile SingularAttribute<ArchivoFirma, String> descripcion;
    public static volatile SingularAttribute<ArchivoFirma, String> nombreArchivo;
    public static volatile SingularAttribute<ArchivoFirma, Integer> estado;
    public static volatile SingularAttribute<ArchivoFirma, String> tipoFirma;
    public static volatile SingularAttribute<ArchivoFirma, Integer> cantidadFirmado;
    public static volatile SingularAttribute<ArchivoFirma, Date> fechaRegistro;
    public static volatile SingularAttribute<ArchivoFirma, String> ip;
    public static volatile SingularAttribute<ArchivoFirma, Long> idUsuario;
    public static volatile SingularAttribute<ArchivoFirma, String> hashArchivo;
    public static volatile ListAttribute<ArchivoFirma, FirmaArchivoUsuario> firmaArchivoUsuarioList;
    public static volatile SingularAttribute<ArchivoFirma, Integer> cantidadConsultas;
    public static volatile SingularAttribute<ArchivoFirma, Long> idArchivoFirma;
    public static volatile SingularAttribute<ArchivoFirma, String> rutaRelativaArchivo;
    public static volatile SingularAttribute<ArchivoFirma, String> emailFirmantes;
    public static volatile SingularAttribute<ArchivoFirma, Integer> cantidadFirmas;

}