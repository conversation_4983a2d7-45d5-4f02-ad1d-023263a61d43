package se.firme.ms.datos.models.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-23T18:28:34")
@StaticMetamodel(PlantillaDocumento.class)
public class PlantillaDocumento_ { 

    public static volatile SingularAttribute<PlantillaDocumento, String> descripcion;
    public static volatile SingularAttribute<PlantillaDocumento, String> nombreArchivo;
    public static volatile SingularAttribute<PlantillaDocumento, String> tipoDocumento;
    public static volatile SingularAttribute<PlantillaDocumento, String> tipoFirma;
    public static volatile SingularAttribute<PlantillaDocumento, String> rutaRelativaArchivo;
    public static volatile SingularAttribute<PlantillaDocumento, String> hashArchivo;
    public static volatile SingularAttribute<PlantillaDocumento, Long> idUsuarioCreador;
    public static volatile SingularAttribute<PlantillaDocumento, Long> idPlantilla;
    public static volatile SingularAttribute<PlantillaDocumento, Boolean> activa;
    public static volatile SingularAttribute<PlantillaDocumento, String> nombrePlantilla;

}