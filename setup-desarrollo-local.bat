@echo off
echo ========================================
echo   CONFIGURACION DESARROLLO LOCAL FIRMESE
echo ========================================
echo.

REM Crear estructura de directorios para archivos
echo 📁 Creando estructura de directorios...
mkdir "C:\Users\<USER>\firmese\file" 2>nul
mkdir "C:\Users\<USER>\firmese\file\1" 2>nul
mkdir "C:\Users\<USER>\firmese\file\tmp" 2>nul

REM Crear directorios por fechas comunes
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "datestamp=%YYYY%%MM%%DD%"

mkdir "C:\Users\<USER>\firmese\file\1\%datestamp%" 2>nul
mkdir "C:\Users\<USER>\firmese\file\2\%datestamp%" 2>nul
mkdir "C:\Users\<USER>\firmese\file\3\%datestamp%" 2>nul

echo ✅ Directorios creados:
echo    - C:\Users\<USER>\firmese\file\
echo    - C:\Users\<USER>\firmese\file\1\%datestamp%\
echo    - C:\Users\<USER>\firmese\file\2\%datestamp%\
echo    - C:\Users\<USER>\firmese\file\3\%datestamp%\
echo.

REM Crear archivo de ejemplo
echo 📄 Creando archivo de ejemplo...
echo ARCHIVO DE PRUEBA PARA DESARROLLO LOCAL > "C:\Users\<USER>\firmese\file\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf"
echo Fecha: %date% %time% >> "C:\Users\<USER>\firmese\file\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf"
echo Este es un archivo generado para pruebas de desarrollo. >> "C:\Users\<USER>\firmese\file\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf"

echo ✅ Archivo de ejemplo creado:
echo    - C:\Users\<USER>\firmese\file\1\%datestamp%\SIGNED_Pruebas notificacion 7.pdf
echo.

echo 🔧 CONFIGURACION ADICIONAL RECOMENDADA:
echo.
echo 1. Verificar que MySQL esté corriendo en localhost:3306
echo 2. Verificar que la base de datos 'firmese_db' exista
echo 3. Configurar servidor SMTP para pruebas (opcional):
echo    - Usar MailHog o similar en localhost:1025
echo    - O configurar Gmail/Outlook para pruebas
echo.
echo 4. Variables de entorno recomendadas:
echo    - JAVA_HOME apuntando a JDK 8 o superior
echo    - MAVEN_HOME configurado correctamente
echo.
echo 📋 COMANDOS UTILES:
echo.
echo Para compilar el proyecto:
echo    mvn clean compile
echo.
echo Para ejecutar tests:
echo    mvn test
echo.
echo Para ejecutar la aplicación:
echo    mvn spring-boot:run
echo.
echo ========================================
echo   CONFIGURACION COMPLETADA
echo ========================================
pause
