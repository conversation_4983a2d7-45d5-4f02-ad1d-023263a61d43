package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.Usuario;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(Token.class)
public class Token_ { 

    public static volatile SingularAttribute<Token, Integer> tipo;
    public static volatile SingularAttribute<Token, String> codigoSms;
    public static volatile SingularAttribute<Token, Date> fechaRegistro;
    public static volatile SingularAttribute<Token, Date> fechaVencimiento;
    public static volatile SingularAttribute<Token, Usuario> idUsuario;
    public static volatile SingularAttribute<Token, String> idToken;
    public static volatile SingularAttribute<Token, String> codigoTransaccion;
    public static volatile SingularAttribute<Token, String> emailFirmante;
    public static volatile SingularAttribute<Token, String> ids;
    public static volatile SingularAttribute<Token, Boolean> activo;
    public static volatile SingularAttribute<Token, Long> idArchivoFirma;

}