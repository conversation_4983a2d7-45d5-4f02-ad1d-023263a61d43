package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.Servicio;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-23T18:28:34")
@StaticMetamodel(PaqueteServicio.class)
public class PaqueteServicio_ { 

    public static volatile SingularAttribute<PaqueteServicio, Integer> idPaqueteServicio;
    public static volatile SingularAttribute<PaqueteServicio, Integer> idSku;
    public static volatile SingularAttribute<PaqueteServicio, Date> fechaRegistro;
    public static volatile SingularAttribute<PaqueteServicio, String> jsonServicio;
    public static volatile SingularAttribute<PaqueteServicio, Servicio> idServicio;
    public static volatile SingularAttribute<PaqueteServicio, Integer> cantidadFirmas;

}