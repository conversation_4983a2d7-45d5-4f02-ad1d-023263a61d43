package se.firme.ms.datos.models.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.Usuario;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(TipoDocumento.class)
public class TipoDocumento_ { 

    public static volatile ListAttribute<TipoDocumento, Usuario> usuarioList;
    public static volatile SingularAttribute<TipoDocumento, String> idTipoDocumento;
    public static volatile SingularAttribute<TipoDocumento, String> nombreTipoDocumento;
    public static volatile SingularAttribute<TipoDocumento, Boolean> activo;

}