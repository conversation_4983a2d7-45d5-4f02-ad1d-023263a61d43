---
type: "manual"
---

APOYO EN EL DESARROLLO DE SOFTWARE
PARA LA PLATAFORMA DE FIRMA ELECTRÓNICA

“FIRMESE” EN VENKO S.A.S

WILSON DANIEL ESCOBAR SILVA

404669

UNIVERSIDAD PONTIFICIA BOLIVARIANA
ESCUELA DE INGENIERÍA

FACULTAD DE INGENIERÍA DE SISTEMAS E INFORMÁTICA

BUCARAMANGA
2025

APOYO EN EL DESARROLLO DE SOFTWARE
PARA LA PLATAFORMA DE FIRMA ELECTRÓNICA

“FIRMESE” EN VENKO S.A.S

WILSON DANIEL ESCOBAR SILVA

404669

DOCUMENTO PRESENTADO COMO PLAN DE TRABAJO DE LA PRÁCTICA

EMPRESARIAL

DOCENTE SUPERVISOR
Nombre del director (Por definir)
Títu<PERSON> del director (Por definir)

SUPERVISOR EMPRESARIAL
ÓSCAR ARMANDO LOZANO AVELLANEDA

UNIVERSIDAD PONTIFICIA BOLIVARIANA
ESCUELA DE INGENIERÍA

FACULTAD DE INGENIERÍA DE SISTEMAS E INFORMÁTICA

BUCARAMANGA
2025

TABLA DE CONTENIDO

1. Generalidades de la empresa 4
1.1. Organización de la empresa 4
1.2. Datos específicos del cargo en el cual se ubica la práctica empresarial. 5
2. Objetivo 5
2.1. Objetivo general 5
2.2. Objetivos específicos 5
3. Actividades para realizar 5
3.1. Objetivos específicos 5
4. Metodología 8

1. Generalidades de la empresa
1.1. Organización de la empresa
Nombre de la Empresa: Venko S.A.S.
Ubicación Geográfica: Bogotá Colombia
Teléfono: 316 0184032
Correo Electrónico: <EMAIL>
Venko S.A.S Una compañía colombiana de Consultoría TI y desarrollo de
aplicaciones con 15 años de experiencia en el mercado, dedicada a brindar
soluciones en tecnología informática, que apoyan los procesos de las
organizaciones.
Productos y Servicios:
La empresa ofrece:
● KONIVIN: Es un Web Service que permite consultar Fuentes Públicas de
información de forma rápida, integrándose fácilmente con su sistema, para
mejorar la productividad del proceso de consulta de información pública de
sus clientes
● Automatización robótica de procesos: Agiliza las tareas repetitivas que
requieren de una o varias personas para llevarlas a cabo ya que ahora
pueden programarse y ejecutarse automáticamente en los sistemas
informáticos de la empresa
● Verifíquese Cedula: Es una aplicación móvil, la cual permite conocer
información publicada en internet relacionada con documentos de
identificación en varios países de latino américa.
● Fírmese: Es un servicio de Firma electrónica, recolección y validación de
información personal con atributos de vigencia, veracidad, calidad y legalidad.
Los datos se recolectan a través de la autorización de datos personales para
su uso y son firmados de manera electrónica.
● Prueba de desempeño en aplicaciones web: Es un servicio con una
metodología comprobada, que le permite a los responsables de sistemas web
encontrar bugs de concurrencia y hacer afinamiento en su plataforma web,
mediante el uso de motores de generación de carga a los sistemas.
● Consultoría en resolución de problemas TIC: Asesoría en tecnologías web
HTTP, configuración de encriptación PKI, optimización de sistemas operativos
Linux y UNIX, ajuste de servidores de aplicaciones Java, verificación de
comunicaciones TCP/IP y políticas de seguridad en redes, y análisis de
problemas relacionados con hardware como servidores y equipos de red.

Misión: Optimizar los procesos de nuestros clientes a partir de soluciones basadas
en tecnologías de información.

1.2. Datos específicos del cargo en el cual se ubica la
práctica empresarial.
Posición: Practicante.
Área: Desarrollo de aplicaciones empresariales
Supervisor empresarial: Oscar Armando Lozano Avellaneda – Arquitecto de TI
Rol asignado: Practicante de desarrollo de software con participación activa en el
diseño, desarrollo, pruebas y documentación de software para soluciones propias de
VENKO, bajo metodologías ágiles.
2. Objetivo
2.1. Objetivo general
Participar activamente en el desarrollo de la plataforma Firmese, una solución
tecnológica para el envío, gestión y firma electrónica de archivos, aplicando buenas
prácticas de ingeniería de software y contribuyendo con código funcional, probado y
documentado en un entorno ágil. Usar los conocimientos adquiridos en el programa de
Ingeniería de Sistemas e informática en el desarrollo de aplicaciones empresariales,
contribuyendo al ciclo completo de desarrollo de software de la empresa VENKO
S.A.S, mediante prácticas de análisis, programación, pruebas, documentación y
despliegue de soluciones tecnológicas.
2.2. Objetivos específicos
 Contribuir al desarrollo de funcionalidades de Firmese en sus módulos backend
(Spring Boot) y frontend (NextJS).
 Apoyar en el diseño y optimización de los procesos de firma electrónica,
autenticación, validación de usuarios y gestión de archivos.
 Implementar pruebas para asegurar la calidad del código antes de despliegue.
 Documentar técnicamente las funcionalidades implementadas y su integración con
el sistema.
 Cumplir con lineamientos de seguridad, arquitectura y buenas prácticas definidas
por el equipo técnico.

3. Actividades para realizar
3.1. Objetivo específico 1
Contribuir al desarrollo de funcionalidades de Firmese en sus módulos backend (Spring
Boot) y frontend (NextJS).
Actividades asociadas:
 Desarrollar endpoints REST en Spring Boot para la gestión de usuarios,
archivos, tokens y procesos de firma.
 Implementar componentes de interfaz en NextJS para las vistas de carga de
documentos, selección de firmantes y estado del proceso de firma.
 Conectarse al backend mediante servicios API REST para consumir
funcionalidades desde el frontend.
 Adaptar el código a los cambios en los requerimientos funcionales dentro de
cada sprint.
 Integrar librerías externas en los módulos respectivos.

3.2. Objetivo específico 2
Apoyar en el diseño y optimización de los procesos de firma electrónica, autenticación,
validación de usuarios y gestión de archivos.
Actividades asociadas:
 Analizar y comprender los flujos actuales de autenticación y firma electrónica en
Firmese.
 Identificar puntos de mejora en los procesos de validación de identidad y
manejo de archivos.
 Proponer y aplicar mejoras en los algoritmos o flujos de verificación, carga y
descarga de archivos.
 Participar en discusiones técnicas sobre mejoras en el proceso de firma
múltiple, OTP o validaciones previas.
 Desarrollar soluciones que optimicen el rendimiento y la experiencia de usuario
final.
3.3. Objetivo específico 3
Implementar pruebas para asegurar la calidad del código antes del despliegue,
verificando que las funcionalidades desarrolladas cumplan con los requerimientos
establecidos.
Actividades asociadas:
 Diseñar y ejecutar pruebas para validar el correcto funcionamiento de las
funcionalidades desarrolladas.
 Identificar posibles errores o comportamientos inesperados antes de su
publicación en ambientes de prueba o producción.
 Participar en el proceso de revisión y validación del software junto al equipo de
desarrollo.
 Asegurar que las funcionalidades se integren correctamente con el resto del
sistema.
 Contribuir a mantener un nivel de calidad adecuado en el producto final.
3.4. Objetivo específico 4
Documentar técnicamente las funcionalidades implementadas y su integración con el
sistema.
Actividades asociadas:
 Redactar documentación técnica de los endpoints y servicios desarrollados
(uso, parámetros, respuestas esperadas).
 Mantener actualizados diagramas de flujo, modelos de datos y flujos
funcionales.
 Registrar en una bitácora diaria las tareas desarrolladas, obstáculos
encontrados y decisiones técnicas tomadas.
 Apoyar en la elaboración de documentación para onboarding de nuevos
desarrolladores.
3.5. Objetivo específico 5
Cumplir con lineamientos de seguridad, arquitectura y buenas prácticas definidas por el
equipo técnico.
Actividades asociadas:
 Utilizar correctamente herramientas de control de versiones (Git, GitLab)

siguiendo flujos definidos (merge requests, revisión de código).
 Aplicar estándares de codificación y nomenclatura establecidos por el equipo
técnico.
 Respetar las políticas de seguridad en el tratamiento de datos sensibles (como
contraseñas, archivos y tokens).
 Aplicar principios de arquitectura limpia, desacoplamiento, y buenas prácticas
de diseño (como SOLID).
 Solicitar revisión de código (code review) y realizar mejoras sugeridas por otros
miembros del equipo.

4. Metodología
La metodología aplicada para el desarrollo y gestión de los proyectos se basa en un
enfoque ágil utilizando el marco de trabajo Scrum. Este enfoque se centra en la iteración y
mejora continua, con el objetivo de optimizar la eficiencia y asegurar que los proyectos
cumplan con los objetivos establecidos de manera efectiva.
El proceso se estructura en las siguientes etapas:
Planificación del Sprint:
● Reunión de Planificación: Al inicio de cada Sprint, se realizará una reunión de
planificación en la que se definirán los objetivos del Sprint y se seleccionarán las
tareas a abordar. Durante esta reunión, se establecerá el Backlog del Sprint,
priorizando las tareas según su importancia y urgencia.
● Definición de Alcance y Tareas: Se detallarán las tareas y objetivos específicos a
alcanzar en el Sprint, y se asignan responsabilidades a los miembros del equipo.
● Registro en Planifique.se: Tanto la planificación como la creación del Backlog del
Sprint se documentará en Planifique.se, la herramienta propia de la empresa para la
gestión de proyectos.
Ejecución del Sprint:
● Desarrollo: Durante el Sprint, el equipo trabajará en la implementación de las tareas
definidas en el Backlog del Sprint. Se mantendrá una comunicación constante para
asegurar el avance, resolver problemas y ajustar tareas según sea necesario.
● Seguimiento Diario: Se realizan reuniones diarias (Daily Scrum) para revisar el
progreso, identificar impedimentos y coordinar acciones para resolver problemas.
Los registros de estas reuniones se llevarán a cabo en Planifique.se para un
seguimiento detallado.
Revisión del Sprint:
● Revisión y Demostración: Al finalizar el Sprint, se llevará a cabo una revisión en la
que el equipo presentará los entregables y funcionalidades implementadas. Se
recibirá retroalimentación del Product Owner y otras partes interesadas para evaluar
el cumplimiento de los objetivos y definir posibles mejoras.
● Documentación y Ajustes: La documentación del proyecto se actualizará con los
resultados de la revisión y se ajustará el Backlog del producto según los
comentarios y necesidades identificadas, registrando toda la información en
Planifique.se.

Retrospectiva del Sprint:
● Evaluación del Sprint: Se realizará una reunión retrospectiva para analizar el
proceso del Sprint, identificar lo que funcionó bien y lo que puede mejorarse. El
equipo discutirá las lecciones aprendidas y propondrá acciones para mejorar la
eficiencia y calidad en futuros Sprints.