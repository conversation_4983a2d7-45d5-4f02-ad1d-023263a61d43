package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.BitacoraRegistro;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;
import se.firme.ms.datos.models.entity.ProcesoFirma;
import se.firme.ms.datos.models.entity.TipoDocumento;
import se.firme.ms.datos.models.entity.Token;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(Usuario.class)
public class Usuario_ { 

    public static volatile SingularAttribute<Usuario, String> numeroCelular;
    public static volatile SingularAttribute<Usuario, String> clave;
    public static volatile SingularAttribute<Usuario, Boolean> estado;
    public static volatile SingularAttribute<Usuario, Date> fechaExpedicionDocumento;
    public static volatile ListAttribute<Usuario, BitacoraRegistro> bitacoraRegistroList;
    public static volatile SingularAttribute<Usuario, Date> fechaRegistro;
    public static volatile SingularAttribute<Usuario, String> documentoPersona;
    public static volatile SingularAttribute<Usuario, Long> idUsuario;
    public static volatile ListAttribute<Usuario, FirmaArchivoUsuario> firmaArchivoUsuarioList;
    public static volatile ListAttribute<Usuario, ProcesoFirma> procesoFirmaList;
    public static volatile SingularAttribute<Usuario, String> nombreCompleto;
    public static volatile SingularAttribute<Usuario, Long> idReferido;
    public static volatile SingularAttribute<Usuario, Boolean> firmadoTyc;
    public static volatile SingularAttribute<Usuario, Boolean> verificadoFuente;
    public static volatile SingularAttribute<Usuario, Date> fechaVerificacion;
    public static volatile SingularAttribute<Usuario, TipoDocumento> idTipoDocumento;
    public static volatile SingularAttribute<Usuario, String> rowData;
    public static volatile SingularAttribute<Usuario, String> obervacionVerificacion;
    public static volatile SingularAttribute<Usuario, String> numeroDocumento;
    public static volatile ListAttribute<Usuario, Token> tokenList;
    public static volatile SingularAttribute<Usuario, String> correoElectronico;
    public static volatile SingularAttribute<Usuario, Boolean> procesoRegistro;
    public static volatile SingularAttribute<Usuario, Boolean> activo;

}