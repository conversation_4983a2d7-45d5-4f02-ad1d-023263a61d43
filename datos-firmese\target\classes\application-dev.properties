# ========================================
# CONFIGURACION DESARROLLO LOCAL - FIRMESE
# ========================================

# Configuración de base de datos para desarrollo
spring.datasource.url=**************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=none

# Configuración de rutas para desarrollo local Windows
routes.custom.file=C:/Users/<USER>/firmese/file

# Configuración de logging para desarrollo
logging.level.se.firme.ms.models.service.ProcesoFirmaServiceImpl=DEBUG
logging.level.se.firme.commons.firmese.service.EmailService=DEBUG
logging.level.se.firme.ms.models.service.TerminosCondicionesService=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Configuración de email para desarrollo (MailHog o similar)
# Descomenta estas líneas si tienes MailHog corriendo
# spring.mail.host=localhost
# spring.mail.port=1025
# spring.mail.username=
# spring.mail.password=
# spring.mail.properties.mail.smtp.auth=false
# spring.mail.properties.mail.smtp.starttls.enable=false

# Configuración de email para Gmail (para pruebas reales)
# Descomenta y configura si quieres usar Gmail
# spring.mail.host=smtp.gmail.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=tu-app-password
# spring.mail.properties.mail.smtp.auth=true
# spring.mail.properties.mail.smtp.starttls.enable=true

# Configuración de servidor para desarrollo
server.port=8080
server.servlet.context-path=/datos-firmese

# Configuración de actuator para monitoreo
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always

# Configuración de Spring Boot DevTools (si está habilitado)
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Configuración de JPA para desarrollo
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Configuración de archivos temporales
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Configuración específica de Firmese para desarrollo
firmese.desarrollo.crear-archivos-prueba=true
firmese.desarrollo.enviar-emails-reales=false
firmese.desarrollo.log-detallado=true
