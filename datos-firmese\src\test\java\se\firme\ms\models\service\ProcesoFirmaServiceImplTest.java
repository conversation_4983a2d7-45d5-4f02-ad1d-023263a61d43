package se.firme.ms.models.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.core.env.Environment;

import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.firmese.util.Parameters;
import se.firme.ms.datos.models.dao.IUsuarioDao;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.interfaz.IFirmaArchivoService;
import se.firme.ms.models.service.interfaz.IServicioService;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * Test unitario para ProcesoFirmaServiceImpl
 * Enfocado en la funcionalidad de filtrado de archivos TyC en notificaciones
 */
@ExtendWith(MockitoExtension.class)
class ProcesoFirmaServiceImplTest {

    @Mock
    private IUsuarioDao iUsuarioDao;
    
    @Mock
    private IServicioService servicioService;
    
    @Mock
    private IFirmaArchivoService firmaArchivoService;
    
    @Mock
    private Environment env;
    
    @Mock
    private Logger logger;
    
    @InjectMocks
    private ProcesoFirmaServiceImpl procesoFirmaService;

    private Usuario usuarioMock;
    private ServicioDTO servicioMock;

    @BeforeEach
    void setUp() {
        usuarioMock = new Usuario();
        usuarioMock.setIdUsuario(1L);
        usuarioMock.setCorreoElectronico("<EMAIL>");
        usuarioMock.setNombreCompleto("Usuario Test");

        servicioMock = new ServicioDTO();
        servicioMock.setNotificarFirma(true);
        servicioMock.setEndpointCBackHabilitado(false);
    }

    @Test
    void testFiltrarArchivosNoTyC_DeberiaExcluirArchivosTyC() throws Exception {
        // Arrange
        List<ArchivoFirma> archivos = crearListaArchivosConTyC();
        
        // Act - Usar reflexión para acceder al método privado
        Method metodoFiltrar = ProcesoFirmaServiceImpl.class.getDeclaredMethod("filtrarArchivosNoTyC", List.class);
        metodoFiltrar.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        List<ArchivoFirma> resultado = (List<ArchivoFirma>) metodoFiltrar.invoke(procesoFirmaService, archivos);
        
        // Assert
        assertEquals(1, resultado.size(), "Debería quedar solo 1 archivo (excluyendo TyC)");
        assertEquals("Propuesta ITECSOFT S.A.S. 2025.docx.pdf", resultado.get(0).getNombreArchivo());
    }

    @Test
    void testEsArchivoTyCPorNombre_DeberiaDetectarTerminosYCondiciones() throws Exception {
        // Act - Usar reflexión para acceder al método privado
        Method metodoEsTyC = ProcesoFirmaServiceImpl.class.getDeclaredMethod("esArchivoTyCPorNombre", String.class);
        metodoEsTyC.setAccessible(true);
        
        // Test casos positivos
        assertTrue((Boolean) metodoEsTyC.invoke(procesoFirmaService, "2025 - TÉRMINOS Y CONDICIONES V2.pdf"));
        assertTrue((Boolean) metodoEsTyC.invoke(procesoFirmaService, "2025. Autorización Datos Personales V3.pdf"));
        assertTrue((Boolean) metodoEsTyC.invoke(procesoFirmaService, "Términos y Condiciones.pdf"));
        assertTrue((Boolean) metodoEsTyC.invoke(procesoFirmaService, "Autorización datos personales.pdf"));
        assertTrue((Boolean) metodoEsTyC.invoke(procesoFirmaService, "TyC_documento.pdf"));
        
        // Test casos negativos
        assertFalse((Boolean) metodoEsTyC.invoke(procesoFirmaService, "Propuesta ITECSOFT S.A.S. 2025.docx.pdf"));
        assertFalse((Boolean) metodoEsTyC.invoke(procesoFirmaService, "Contrato de servicios.pdf"));
        assertFalse((Boolean) metodoEsTyC.invoke(procesoFirmaService, "Factura 001.pdf"));
    }

    @Test
    void testNotificarFirma_NoDeberiaNotificarSoloConArchivosTyC() throws Exception {
        // Arrange
        List<ArchivoFirma> soloArchivosTyC = crearListaSoloArchivosTyC();
        
        when(iUsuarioDao.findById(anyLong())).thenReturn(Optional.of(usuarioMock));
        when(servicioService.findById(anyLong())).thenReturn(servicioMock);
        
        // Act - Usar reflexión para acceder al método privado
        Method metodoNotificar = ProcesoFirmaServiceImpl.class.getDeclaredMethod("notificarFirma", List.class, Usuario.class);
        metodoNotificar.setAccessible(true);
        
        // No debería lanzar excepción y debería terminar temprano
        assertDoesNotThrow(() -> {
            metodoNotificar.invoke(procesoFirmaService, soloArchivosTyC, usuarioMock);
        });
        
        // Verificar que no se intentó enviar emails (esto requeriría mockear EmailService)
        // En un test más completo, se podría verificar que EmailService.enviarCorreo no fue llamado
    }

    @Test
    void testGenerarContenidoNotificacionFirma_DeberiaUsarTemplate() throws Exception {
        // Arrange
        List<ArchivoFirma> archivos = crearListaArchivosNoTyC();
        
        // Act - Usar reflexión para acceder al método privado
        Method metodoGenerar = ProcesoFirmaServiceImpl.class.getDeclaredMethod("generarContenidoNotificacionFirma", List.class);
        metodoGenerar.setAccessible(true);
        
        String resultado = (String) metodoGenerar.invoke(procesoFirmaService, archivos);
        
        // Assert
        assertNotNull(resultado);
        assertTrue(resultado.contains("Estimado usuario"));
        assertTrue(resultado.contains("Se ha completado el proceso de firma"));
        assertTrue(resultado.contains("Propuesta ITECSOFT S.A.S. 2025.docx.pdf"));
        assertFalse(resultado.contains("TÉRMINOS Y CONDICIONES"));
        assertFalse(resultado.contains("Autorización Datos Personales"));
    }

    // Métodos auxiliares para crear datos de prueba
    
    private List<ArchivoFirma> crearListaArchivosConTyC() {
        List<ArchivoFirma> archivos = new ArrayList<>();
        
        // Archivo TyC 1
        ArchivoFirma tyc1 = new ArchivoFirma();
        tyc1.setIdArchivoFirma(1L);
        tyc1.setNombreArchivo("2025 - TÉRMINOS Y CONDICIONES V2.pdf");
        tyc1.setEstado(Parameters.string.ESTADO_ARCHIVO_FIRMADO);
        tyc1.setIdUsuario(1L);
        archivos.add(tyc1);
        
        // Archivo TyC 2
        ArchivoFirma tyc2 = new ArchivoFirma();
        tyc2.setIdArchivoFirma(2L);
        tyc2.setNombreArchivo("2025. Autorización Datos Personales V3.pdf");
        tyc2.setEstado(Parameters.string.ESTADO_ARCHIVO_FIRMADO);
        tyc2.setIdUsuario(1L);
        archivos.add(tyc2);
        
        // Archivo real
        ArchivoFirma archivoReal = new ArchivoFirma();
        archivoReal.setIdArchivoFirma(3L);
        archivoReal.setNombreArchivo("Propuesta ITECSOFT S.A.S. 2025.docx.pdf");
        archivoReal.setEstado(Parameters.string.ESTADO_ARCHIVO_PENDIENTE_FIRMA);
        archivoReal.setIdUsuario(1L);
        archivos.add(archivoReal);
        
        return archivos;
    }
    
    private List<ArchivoFirma> crearListaSoloArchivosTyC() {
        List<ArchivoFirma> archivos = new ArrayList<>();
        
        ArchivoFirma tyc = new ArchivoFirma();
        tyc.setIdArchivoFirma(1L);
        tyc.setNombreArchivo("2025 - TÉRMINOS Y CONDICIONES V2.pdf");
        tyc.setEstado(Parameters.string.ESTADO_ARCHIVO_FIRMADO);
        tyc.setIdUsuario(1L);
        archivos.add(tyc);
        
        return archivos;
    }
    
    private List<ArchivoFirma> crearListaArchivosNoTyC() {
        List<ArchivoFirma> archivos = new ArrayList<>();
        
        ArchivoFirma archivo = new ArchivoFirma();
        archivo.setIdArchivoFirma(1L);
        archivo.setNombreArchivo("Propuesta ITECSOFT S.A.S. 2025.docx.pdf");
        archivo.setEstado(Parameters.string.ESTADO_ARCHIVO_FIRMADO);
        archivo.setIdUsuario(1L);
        archivos.add(archivo);
        
        return archivos;
    }
}
