/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

/**
 * DTO para representar archivos en notificaciones de email de procesos de firma completados.
 * 
 * Este DTO encapsula la información necesaria para mostrar archivos en las notificaciones
 * por correo electrónico cuando se completa un proceso de firma, incluyendo el nombre
 * del archivo y su estado de firma.
 * 
 * Utilizado principalmente por EmailTemplateService para generar templates de notificación
 * y por ProcesoFirmaServiceImpl para estructurar datos de archivos firmados.
 * 
 *
 */
public class NotificacionArchivoEmailDTO {
    
    private String nombreArchivo;
    
    /**
     * Estado textual del archivo (ej: "FIRMADO", "NO FIRMADO")
     */
    private String estadoTexto;

    public NotificacionArchivoEmailDTO() {

    }

    public NotificacionArchivoEmailDTO(String nombreArchivo, String estadoTexto) {
        this.nombreArchivo = nombreArchivo;
        this.estadoTexto = estadoTexto;
    }

    public String getNombreArchivo() {
        return nombreArchivo;
    }

    public void setNombreArchivo(String nombreArchivo) {
        this.nombreArchivo = nombreArchivo;
    }

    public String getEstadoTexto() {
        return estadoTexto;
    }

    public void setEstadoTexto(String estadoTexto) {
        this.estadoTexto = estadoTexto;
    }

    @Override
    public String toString() {
        return "NotificacionArchivoEmailDTO{" +
                "nombreArchivo='" + nombreArchivo + '\'' +
                ", estadoTexto='" + estadoTexto + '\'' +
                '}';
    }
}
