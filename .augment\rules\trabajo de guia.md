---
type: "manual"
---

**Mantenimiento del sistema de gestión documental en Control Online
S.A.S**

**<PERSON>**

![](media/image1.png){width="1.2955216535433072in" height="0.625in"}

**Trabajo de grado para optar el título de**

**Ingeniero en Sistemas e informática**

**Universidad Pontificia Bolivariana**

**Escuela de Ingenierías**

**Ingeniería de Sistemas e Informática**

**Bucaramanga**

**2025**

**Mantenimiento del sistema de gestión documental en Control Online
S.A.S**

**<PERSON>**

**Trabajo de grado para optar el título de**

**Ingeniero en Sistemas e informática**

**Director**

**<PERSON><PERSON>**

**Ingeniera de Sistemas e Informática**

**Universidad Pontificia Bolivariana**

**Escuela de Ingenierías**

**Ingeniería de Sistemas e Informática**

**Bucaramanga**

**2025**

**Contenido**

[Introducción [10](#introducción)](#introducción)

[Generalidades de la empresa
[12](#generalidades-de-la-empresa)](#generalidades-de-la-empresa)

[Estructura organizacional
[13](#estructura-organizacional)](#estructura-organizacional)

[Planteamiento del problema
[15](#planteamiento-del-problema)](#planteamiento-del-problema)

[Objetivos [16](#objetivos)](#objetivos)

[Objetivo general [16](#objetivo-general)](#objetivo-general)

[Objetivos específicos
[16](#objetivos-específicos)](#objetivos-específicos)

[Justificación [17](#justificación)](#justificación)

[Marco conceptual [18](#marco-conceptual)](#marco-conceptual)

[Sistema de Gestión de Documentos Electrónicos (SGDEA)
[18](#sistema-de-gestión-de-documentos-electrónicos-sgdea)](#sistema-de-gestión-de-documentos-electrónicos-sgdea)

[Documento electrónico
[18](#documento-electrónico)](#documento-electrónico)

[Arquitectura limpia [18](#arquitectura-limpia)](#arquitectura-limpia)

[Web Api [19](#web-api)](#web-api)

[Base de datos [19](#base-de-datos)](#base-de-datos)

[Marco tecnológico [20](#marco-tecnológico)](#marco-tecnológico)

[Domain Driven Desing
[20](#domain-driven-desing)](#domain-driven-desing)

[Patrón de diseño repositorio
[20](#patrón-de-diseño-repositorio)](#patrón-de-diseño-repositorio)

[ASP.NET Core [21](#asp.net-core)](#asp.net-core)

[Blazor [21](#blazor)](#blazor)

[Entity Framework Core
[22](#entity-framework-core)](#entity-framework-core)

[GitHub [23](#github)](#github)

[Telerik [24](#telerik)](#telerik)

[Metodología [25](#metodología)](#metodología)

[Roles y estructura del equipo
[25](#roles-y-estructura-del-equipo)](#roles-y-estructura-del-equipo)

[Ciclo de desarrollo en ControlDoc
[26](#ciclo-de-desarrollo-en-controldoc)](#ciclo-de-desarrollo-en-controldoc)

[Levantamiento y documentación de requerimientos
[26](#levantamiento-y-documentación-de-requerimientos)](#levantamiento-y-documentación-de-requerimientos)

[Planificación del Sprint (Sprint Planning)
[26](#planificación-del-sprint-sprint-planning)](#planificación-del-sprint-sprint-planning)

[Ejecución y reuniones diarias (Daily Scrum)
[26](#ejecución-y-reuniones-diarias-daily-scrum)](#ejecución-y-reuniones-diarias-daily-scrum)

[Pruebas QA (Quality Assurance)
[26](#pruebas-qa-quality-assurance)](#pruebas-qa-quality-assurance)

[Pruebas UAT (User Acceptance Testing)
[27](#pruebas-uat-user-acceptance-testing)](#pruebas-uat-user-acceptance-testing)

[Cierre del Sprint y liberación a producción
[27](#cierre-del-sprint-y-liberación-a-producción)](#cierre-del-sprint-y-liberación-a-producción)

[Herramientas y gestión del flujo de trabajo
[27](#herramientas-y-gestión-del-flujo-de-trabajo)](#herramientas-y-gestión-del-flujo-de-trabajo)

[Representación gráfica del proceso metodológico
[27](#representación-gráfica-del-proceso-metodológico)](#representación-gráfica-del-proceso-metodológico)

[Cronograma de actividades
[30](#cronograma-de-actividades)](#cronograma-de-actividades)

[Presupuesto [31](#presupuesto)](#presupuesto)

[Resultados [32](#resultados)](#resultados)

[Funciones como desarrollador en la empresa
[32](#funciones-como-desarrollador-en-la-empresa)](#funciones-como-desarrollador-en-la-empresa)

[Soporte y corrección de bugs
[32](#soporte-y-corrección-de-bugs)](#soporte-y-corrección-de-bugs)

[Desarrollo de nuevas funcionalidades
[33](#desarrollo-de-nuevas-funcionalidades)](#desarrollo-de-nuevas-funcionalidades)

[Aporte al proceso de desarrollo
[33](#aporte-al-proceso-de-desarrollo)](#aporte-al-proceso-de-desarrollo)

[Acerca del software ControlDoc y su implementación
[34](#acerca-del-software-controldoc-y-su-implementación)](#acerca-del-software-controldoc-y-su-implementación)

[Modalidades de implementación
[35](#modalidades-de-implementación)](#modalidades-de-implementación)

[Despliegue On-Premises
[35](#despliegue-on-premises)](#despliegue-on-premises)

[Despliegue en la nube (Azure Cloud)
[36](#despliegue-en-la-nube-azure-cloud)](#despliegue-en-la-nube-azure-cloud)

[Actividades realizadas
[37](#actividades-realizadas)](#actividades-realizadas)

[Fase de capacitación
[38](#fase-de-capacitación)](#fase-de-capacitación)

[Curso de Domain-Driven Design (DDD) en ASP.NET
[38](#curso-de-domain-driven-design-ddd-en-asp.net)](#curso-de-domain-driven-design-ddd-en-asp.net)

[Curso de Blazor [38](#curso-de-blazor)](#curso-de-blazor)

[Proyecto práctico: API y Frontend en Blazor
[38](#proyecto-práctico-api-y-frontend-en-blazor)](#proyecto-práctico-api-y-frontend-en-blazor)

[Actividades funcionales desarrolladas
[38](#actividades-funcionales-desarrolladas)](#actividades-funcionales-desarrolladas)

[Inclusión de gestor y visualización de manuales
[38](#inclusión-de-gestor-y-visualización-de-manuales)](#inclusión-de-gestor-y-visualización-de-manuales)

[Inclusión de gestor y visualización de tutoriales
[39](#inclusión-de-gestor-y-visualización-de-tutoriales)](#inclusión-de-gestor-y-visualización-de-tutoriales)

[Ampliación y suspensión de términos de vencimiento
[40](#ampliación-y-suspensión-de-términos-de-vencimiento)](#ampliación-y-suspensión-de-términos-de-vencimiento)

[Historial de suspensión de documentos:
[42](#historial-de-suspensión-de-documentos)](#historial-de-suspensión-de-documentos)

[Historial de ampliación de términos:
[42](#historial-de-ampliación-de-términos)](#historial-de-ampliación-de-términos)

[Lógica condicional en el selector de fechas (ampliación de términos):
[43](#lógica-condicional-en-el-selector-de-fechas-ampliación-de-términos)](#lógica-condicional-en-el-selector-de-fechas-ampliación-de-términos)

[Dinámica de etiquetas en el módulo PQRSd:
[44](#dinámica-de-etiquetas-en-el-módulo-pqrsd)](#dinámica-de-etiquetas-en-el-módulo-pqrsd)

[Componente de carga para descarga de imágenes en expedientes
[45](#componente-de-carga-para-descarga-de-imágenes-en-expedientes)](#componente-de-carga-para-descarga-de-imágenes-en-expedientes)

[Botón para limpiar indexación de expedientes
[45](#botón-para-limpiar-indexación-de-expedientes)](#botón-para-limpiar-indexación-de-expedientes)

[Corrección de errores (Bugs)
[46](#corrección-de-errores-bugs)](#corrección-de-errores-bugs)

[Ajuste en el manejo de errores al tramitar documentos:
[46](#ajuste-en-el-manejo-de-errores-al-tramitar-documentos)](#ajuste-en-el-manejo-de-errores-al-tramitar-documentos)

[Duplicación de tarjetas al desanular documentos:
[46](#duplicación-de-tarjetas-al-desanular-documentos)](#duplicación-de-tarjetas-al-desanular-documentos)

[No actualiza las tarjetas tras aplicar filtros (bandeja de
digitalización):
[46](#no-actualiza-las-tarjetas-tras-aplicar-filtros-bandeja-de-digitalización)](#no-actualiza-las-tarjetas-tras-aplicar-filtros-bandeja-de-digitalización)

[Error en la visualización de copias en la bandeja de tareas
documentales:
[47](#error-en-la-visualización-de-copias-en-la-bandeja-de-tareas-documentales)](#error-en-la-visualización-de-copias-en-la-bandeja-de-tareas-documentales)

[Exclusión de documentos anulados en la bandeja de tareas:
[47](#exclusión-de-documentos-anulados-en-la-bandeja-de-tareas)](#exclusión-de-documentos-anulados-en-la-bandeja-de-tareas)

[Validación del límite de folios al cargar imágenes:
[47](#validación-del-límite-de-folios-al-cargar-imágenes)](#validación-del-límite-de-folios-al-cargar-imágenes)

[Fallo en el botón de eliminación de usuarios del modal de
clasificación:
[47](#fallo-en-el-botón-de-eliminación-de-usuarios-del-modal-de-clasificación)](#fallo-en-el-botón-de-eliminación-de-usuarios-del-modal-de-clasificación)

[Conclusiones [48](#conclusiones)](#conclusiones)

[Referencias [50](#referencias)](#referencias)

**\
**

**Lista de figuras**

[Figura 1 Estructura organizacional de Control Online International.
[14](#_Toc205131363)](#_Toc205131363)

[Figura 2 Marco de trabajo Scrum aplicado
[28](#_Toc205131364)](#_Toc205131364)

[Figura 3 Flujo de trabajo desde requerimiento hasta producción
[28](#_Toc205131365)](#_Toc205131365)

[Figura 4 Vista de Microsoft Planner utilizada en la gestión de tareas
[29](#_Toc205131366)](#_Toc205131366)

[Figura 5 Diagrama de la arquitectura de microservicios de Control Doc
[34](#_Toc205131367)](#_Toc205131367)

[Figura 6 Diagrama de despliegue On premises de Control Doc
[36](#_Toc205131368)](#_Toc205131368)

[Figura 7 Diagrama de despliegue Cloud Azure de Control Doc
[37](#_Toc205131369)](#_Toc205131369)

[Figura 8 Gestor de manuales [39](#_Toc205131370)](#_Toc205131370)

[Figura 9 Apartado de visualización de manuales
[39](#_Toc205131371)](#_Toc205131371)

[Figura 10 Gestor de tutoriales [40](#_Toc205131372)](#_Toc205131372)

[Figura 11 Apartado de visualización de tutoriales
[40](#_Toc205131373)](#_Toc205131373)

[Figura 12 Modal de ampliación de términos
[41](#_Toc205131374)](#_Toc205131374)

[Figura 13 Modal de suspensión de términos
[41](#_Toc205131375)](#_Toc205131375)

[Figura 14 Historial de suspensión [42](#_Toc205131376)](#_Toc205131376)

[Figura 15 Historial de ampliación [43](#_Toc205131377)](#_Toc205131377)

[Figura 16 Historial de ampliación con lógica adicional
[43](#_Toc205131378)](#_Toc205131378)

[Figura 17 Vista PQR con llave activa
[44](#_Toc205131379)](#_Toc205131379)

[Figura 18 Vista PQR sin llave activada
[44](#_Toc205131380)](#_Toc205131380)

[Figura 19 Spiner Loader de descarga de imagen.
[45](#_Toc205131381)](#_Toc205131381)

[Figura 20 Diagrama de despliegue Cloud Azure de Control Doc
[46](#_Toc205131382)](#_Toc205131382)

**\
**

**Lista de tablas**

[Tabla 1 Cronograma ejecución actividades propuestas
[30](#_bookmark24)](#_bookmark24)

[Tabla 2 Presupuesto proyecto [31](#_Toc205131404)](#_Toc205131404)

**\
**

+-------------+-----+---+----------------------------------------------+
| **\         |     |   | Resumen general de trabajo de grado en       |
| *           |     |   | español                                      |
| *![Admintro |     |   |                                              |
| logo](m     |     |   |                                              |
| edia/image2 |     |   |                                              |
| .png){width |     |   |                                              |
| ="2.0486111 |     |   |                                              |
| 11111111in" |     |   |                                              |
| height="    |     |   |                                              |
| 0.708333333 |     |   |                                              |
| 3333334in"} |     |   |                                              |
+=============+=====+===+==============================================+
| **TITULO:** |     | M |                                              |
|             |     | a |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | i |                                              |
|             |     | m |                                              |
|             |     | i |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | o |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | l |                                              |
|             |     | s |                                              |
|             |     | i |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | m |                                              |
|             |     | a |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | g |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | i |                                              |
|             |     | ó |                                              |
|             |     | n |                                              |
|             |     | d |                                              |
|             |     | o |                                              |
|             |     | c |                                              |
|             |     | u |                                              |
|             |     | m |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | a |                                              |
|             |     | l |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | c |                                              |
|             |     | o |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
|             |     | l |                                              |
|             |     | o |                                              |
|             |     | n |                                              |
|             |     | l |                                              |
|             |     | i |                                              |
|             |     | n |                                              |
|             |     | e |                                              |
|             |     | S |                                              |
|             |     | . |                                              |
|             |     | A |                                              |
|             |     | . |                                              |
|             |     | S |                                              |
+-------------+-----+---+----------------------------------------------+
|             |     |   |                                              |
+-------------+-----+---+----------------------------------------------+
| **A         |     | J |                                              |
| UTOR(ES):** |     | u |                                              |
|             |     | a |                                              |
|             |     | n |                                              |
|             |     | S |                                              |
|             |     | e |                                              |
|             |     | b |                                              |
|             |     | a |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | i |                                              |
|             |     | a |                                              |
|             |     | n |                                              |
|             |     | T |                                              |
|             |     | a |                                              |
|             |     | r |                                              |
|             |     | a |                                              |
|             |     | z |                                              |
|             |     | o |                                              |
|             |     | n |                                              |
|             |     | a |                                              |
|             |     | Q |                                              |
|             |     | u |                                              |
|             |     | i |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
+-------------+-----+---+----------------------------------------------+
|             |     |   |                                              |
+-------------+-----+---+----------------------------------------------+
| **          |     | F |                                              |
| PROGRAMA:** |     | a |                                              |
|             |     | c |                                              |
|             |     | u |                                              |
|             |     | l |                                              |
|             |     | t |                                              |
|             |     | a |                                              |
|             |     | d |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | I |                                              |
|             |     | n |                                              |
|             |     | g |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | i |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | í |                                              |
|             |     | a |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | S |                                              |
|             |     | i |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | m |                                              |
|             |     | a |                                              |
|             |     | s |                                              |
|             |     | e |                                              |
|             |     | I |                                              |
|             |     | n |                                              |
|             |     | f |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | m |                                              |
|             |     | á |                                              |
|             |     | t |                                              |
|             |     | i |                                              |
|             |     | c |                                              |
|             |     | a |                                              |
+-------------+-----+---+----------------------------------------------+
|             |     |   |                                              |
+-------------+-----+---+----------------------------------------------+
| **DIR       |     | Y |                                              |
| ECTOR(A):** |     | u |                                              |
|             |     | r |                                              |
|             |     | l |                                              |
|             |     | e |                                              |
|             |     | y |                                              |
|             |     | E |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | f |                                              |
|             |     | a |                                              |
|             |     | n |                                              |
|             |     | y |                                              |
|             |     | R |                                              |
|             |     | u |                                              |
|             |     | e |                                              |
|             |     | d |                                              |
|             |     | a |                                              |
|             |     | R |                                              |
|             |     | o |                                              |
|             |     | m |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
+-------------+-----+---+----------------------------------------------+
|             |     |   |                                              |
+-------------+-----+---+----------------------------------------------+
| **RESUMEN** |     |   |                                              |
+-------------+-----+---+----------------------------------------------+
| Este        |     |   |                                              |
| informe     |     |   |                                              |
| final       |     |   |                                              |
| presenta    |     |   |                                              |
| los         |     |   |                                              |
| resultados  |     |   |                                              |
| obtenidos   |     |   |                                              |
| durante el  |     |   |                                              |
| desarrollo  |     |   |                                              |
| de las      |     |   |                                              |
| prácticas   |     |   |                                              |
| em          |     |   |                                              |
| presariales |     |   |                                              |
| realizadas  |     |   |                                              |
| en la       |     |   |                                              |
| empresa     |     |   |                                              |
| Control     |     |   |                                              |
| Online      |     |   |                                              |
| In          |     |   |                                              |
| ternational |     |   |                                              |
| S.A.S.,     |     |   |                                              |
| enfocadas   |     |   |                                              |
| en el       |     |   |                                              |
| man         |     |   |                                              |
| tenimiento, |     |   |                                              |
| mejora e    |     |   |                                              |
| imp         |     |   |                                              |
| lementación |     |   |                                              |
| de nuevas   |     |   |                                              |
| func        |     |   |                                              |
| ionalidades |     |   |                                              |
| del sistema |     |   |                                              |
| de gestión  |     |   |                                              |
| documental  |     |   |                                              |
| ControlDoc. |     |   |                                              |
| A lo largo  |     |   |                                              |
| del         |     |   |                                              |
| proceso, se |     |   |                                              |
| ejecutaron  |     |   |                                              |
| tareas      |     |   |                                              |
| orientadas  |     |   |                                              |
| a la        |     |   |                                              |
| corrección  |     |   |                                              |
| de errores, |     |   |                                              |
| creación de |     |   |                                              |
| módulos     |     |   |                                              |
| f           |     |   |                                              |
| uncionales, |     |   |                                              |
| integración |     |   |                                              |
| de          |     |   |                                              |
| componentes |     |   |                                              |
| para la     |     |   |                                              |
| gestión de  |     |   |                                              |
| manuales,   |     |   |                                              |
| tutoriales, |     |   |                                              |
| y           |     |   |                                              |
| adm         |     |   |                                              |
| inistración |     |   |                                              |
| de          |     |   |                                              |
| ve          |     |   |                                              |
| ncimientos. |     |   |                                              |
| Las         |     |   |                                              |
| actividades |     |   |                                              |
| se          |     |   |                                              |
| es          |     |   |                                              |
| tructuraron |     |   |                                              |
| bajo una    |     |   |                                              |
| metodología |     |   |                                              |
| basada en   |     |   |                                              |
| el marco de |     |   |                                              |
| trabajo     |     |   |                                              |
| Scrum, lo   |     |   |                                              |
| cual        |     |   |                                              |
| permitió    |     |   |                                              |
| una         |     |   |                                              |
| pl          |     |   |                                              |
| anificación |     |   |                                              |
| iterativa   |     |   |                                              |
| mediante    |     |   |                                              |
| Sprints y   |     |   |                                              |
| la          |     |   |                                              |
| validación  |     |   |                                              |
| progresiva  |     |   |                                              |
| del         |     |   |                                              |
| software a  |     |   |                                              |
| través de   |     |   |                                              |
| pruebas QA  |     |   |                                              |
| y UAT.      |     |   |                                              |
|             |     |   |                                              |
| Este        |     |   |                                              |
| documento   |     |   |                                              |
| expone el   |     |   |                                              |
| contexto    |     |   |                                              |
| org         |     |   |                                              |
| anizacional |     |   |                                              |
| y técnico   |     |   |                                              |
| en el que   |     |   |                                              |
| se          |     |   |                                              |
| de          |     |   |                                              |
| sarrollaron |     |   |                                              |
| las         |     |   |                                              |
| a           |     |   |                                              |
| ctividades, |     |   |                                              |
| así como el |     |   |                                              |
| impacto de  |     |   |                                              |
| los         |     |   |                                              |
| resultados  |     |   |                                              |
| obtenidos   |     |   |                                              |
| en el       |     |   |                                              |
| entorno     |     |   |                                              |
| productivo. |     |   |                                              |
|             |     |   |                                              |
| Finalmente, |     |   |                                              |
| se destaca  |     |   |                                              |
| la          |     |   |                                              |
| evolución   |     |   |                                              |
| del proceso |     |   |                                              |
| de          |     |   |                                              |
| aprendizaje |     |   |                                              |
| y la        |     |   |                                              |
| co          |     |   |                                              |
| nsolidación |     |   |                                              |
| de          |     |   |                                              |
| c           |     |   |                                              |
| ompetencias |     |   |                                              |
| pr          |     |   |                                              |
| ofesionales |     |   |                                              |
| en el área  |     |   |                                              |
| de          |     |   |                                              |
| ingeniería  |     |   |                                              |
| de sistemas |     |   |                                              |
| e           |     |   |                                              |
| i           |     |   |                                              |
| nformática. |     |   |                                              |
+-------------+-----+---+----------------------------------------------+
|             |     |   |                                              |
+-------------+-----+---+----------------------------------------------+
| **PALABRAS  |     |   |                                              |
| CLAVE:**    |     |   |                                              |
+-------------+-----+---+----------------------------------------------+
|             |     | C |                                              |
|             |     | o |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
|             |     | l |                                              |
|             |     | D |                                              |
|             |     | o |                                              |
|             |     | c |                                              |
|             |     | , |                                              |
|             |     | G |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | i |                                              |
|             |     | ó |                                              |
|             |     | n |                                              |
|             |     | d |                                              |
|             |     | o |                                              |
|             |     | c |                                              |
|             |     | u |                                              |
|             |     | m |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | a |                                              |
|             |     | l |                                              |
|             |     | , |                                              |
|             |     | S |                                              |
|             |     | i |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | m |                                              |
|             |     | a |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | g |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | i |                                              |
|             |     | ó |                                              |
|             |     | n |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | d |                                              |
|             |     | o |                                              |
|             |     | c |                                              |
|             |     | u |                                              |
|             |     | m |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | o |                                              |
|             |     | s |                                              |
|             |     | e |                                              |
|             |     | l |                                              |
|             |     | e |                                              |
|             |     | c |                                              |
|             |     | t |                                              |
|             |     | r |                                              |
|             |     | ó |                                              |
|             |     | n |                                              |
|             |     | i |                                              |
|             |     | c |                                              |
|             |     | o |                                              |
|             |     | s |                                              |
|             |     | , |                                              |
|             |     | S |                                              |
|             |     | G |                                              |
|             |     | D |                                              |
|             |     | E |                                              |
|             |     | A |                                              |
|             |     | , |                                              |
|             |     | A |                                              |
|             |     | S |                                              |
|             |     | P |                                              |
|             |     | . |                                              |
|             |     | N |                                              |
|             |     | E |                                              |
|             |     | T |                                              |
|             |     | C |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | e |                                              |
|             |     | , |                                              |
|             |     | B |                                              |
|             |     | l |                                              |
|             |     | a |                                              |
|             |     | z |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | , |                                              |
|             |     | E |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | i |                                              |
|             |     | t |                                              |
|             |     | y |                                              |
|             |     | F |                                              |
|             |     | r |                                              |
|             |     | a |                                              |
|             |     | m |                                              |
|             |     | e |                                              |
|             |     | w |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | k |                                              |
|             |     | C |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | e |                                              |
|             |     | , |                                              |
|             |     | A |                                              |
|             |     | r |                                              |
|             |     | q |                                              |
|             |     | u |                                              |
|             |     | i |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | c |                                              |
|             |     | t |                                              |
|             |     | u |                                              |
|             |     | r |                                              |
|             |     | a |                                              |
|             |     | l |                                              |
|             |     | i |                                              |
|             |     | m |                                              |
|             |     | p |                                              |
|             |     | i |                                              |
|             |     | a |                                              |
|             |     | , |                                              |
|             |     | D |                                              |
|             |     | o |                                              |
|             |     | m |                                              |
|             |     | a |                                              |
|             |     | i |                                              |
|             |     | n |                                              |
|             |     | D |                                              |
|             |     | r |                                              |
|             |     | i |                                              |
|             |     | v |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | D |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | i |                                              |
|             |     | g |                                              |
|             |     | n |                                              |
|             |     | , |                                              |
|             |     | D |                                              |
|             |     | D |                                              |
|             |     | D |                                              |
|             |     | , |                                              |
|             |     | M |                                              |
|             |     | i |                                              |
|             |     | c |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
|             |     | s |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | v |                                              |
|             |     | i |                                              |
|             |     | c |                                              |
|             |     | i |                                              |
|             |     | o |                                              |
|             |     | s |                                              |
|             |     | , |                                              |
|             |     | A |                                              |
|             |     | P |                                              |
|             |     | I |                                              |
|             |     | G |                                              |
|             |     | a |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | w |                                              |
|             |     | a |                                              |
|             |     | y |                                              |
|             |     | , |                                              |
|             |     | A |                                              |
|             |     | z |                                              |
|             |     | u |                                              |
|             |     | r |                                              |
|             |     | e |                                              |
|             |     | C |                                              |
|             |     | l |                                              |
|             |     | o |                                              |
|             |     | u |                                              |
|             |     | d |                                              |
|             |     | , |                                              |
|             |     | S |                                              |
|             |     | c |                                              |
|             |     | r |                                              |
|             |     | u |                                              |
|             |     | m |                                              |
|             |     | , |                                              |
|             |     | S |                                              |
|             |     | p |                                              |
|             |     | r |                                              |
|             |     | i |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | , |                                              |
|             |     | Q |                                              |
|             |     | A |                                              |
|             |     | , |                                              |
|             |     | U |                                              |
|             |     | A |                                              |
|             |     | T |                                              |
|             |     | , |                                              |
|             |     | P |                                              |
|             |     | a |                                              |
|             |     | t |                                              |
|             |     | r |                                              |
|             |     | ó |                                              |
|             |     | n |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | d |                                              |
|             |     | i |                                              |
|             |     | s |                                              |
|             |     | e |                                              |
|             |     | ñ |                                              |
|             |     | o |                                              |
|             |     | R |                                              |
|             |     | e |                                              |
|             |     | p |                                              |
|             |     | o |                                              |
|             |     | s |                                              |
|             |     | i |                                              |
|             |     | t |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | i |                                              |
|             |     | o |                                              |
|             |     | , |                                              |
|             |     | T |                                              |
|             |     | e |                                              |
|             |     | l |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | i |                                              |
|             |     | k |                                              |
|             |     | U |                                              |
|             |     | I |                                              |
|             |     | f |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | B |                                              |
|             |     | l |                                              |
|             |     | a |                                              |
|             |     | z |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | , |                                              |
|             |     | M |                                              |
|             |     | i |                                              |
|             |     | c |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
|             |     | s |                                              |
|             |     | o |                                              |
|             |     | f |                                              |
|             |     | t |                                              |
|             |     | P |                                              |
|             |     | l |                                              |
|             |     | a |                                              |
|             |     | n |                                              |
|             |     | n |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | , |                                              |
|             |     | G |                                              |
|             |     | i |                                              |
|             |     | t |                                              |
|             |     | H |                                              |
|             |     | u |                                              |
|             |     | b |                                              |
|             |     | , |                                              |
|             |     | K |                                              |
|             |     | u |                                              |
|             |     | b |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | n |                                              |
|             |     | e |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | , |                                              |
|             |     | C |                                              |
|             |     | o |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
|             |     | l |                                              |
|             |     | O |                                              |
|             |     | n |                                              |
|             |     | l |                                              |
|             |     | i |                                              |
|             |     | n |                                              |
|             |     | e |                                              |
|             |     | I |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | n |                                              |
|             |     | a |                                              |
|             |     | t |                                              |
|             |     | i |                                              |
|             |     | o |                                              |
|             |     | n |                                              |
|             |     | a |                                              |
|             |     | l |                                              |
|             |     | S |                                              |
|             |     | . |                                              |
|             |     | A |                                              |
|             |     | . |                                              |
|             |     | S |                                              |
|             |     | . |                                              |
|             |     | , |                                              |
|             |     | M |                                              |
|             |     | a |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | i |                                              |
|             |     | m |                                              |
|             |     | i |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | o |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | o |                                              |
|             |     | f |                                              |
|             |     | t |                                              |
|             |     | w |                                              |
|             |     | a |                                              |
|             |     | r |                                              |
|             |     | e |                                              |
|             |     | , |                                              |
|             |     | D |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | a |                                              |
|             |     | r |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
|             |     | l |                                              |
|             |     | l |                                              |
|             |     | o |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | f |                                              |
|             |     | u |                                              |
|             |     | n |                                              |
|             |     | c |                                              |
|             |     | i |                                              |
|             |     | o |                                              |
|             |     | n |                                              |
|             |     | a |                                              |
|             |     | l |                                              |
|             |     | i |                                              |
|             |     | d |                                              |
|             |     | a |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | , |                                              |
|             |     | C |                                              |
|             |     | o |                                              |
|             |     | r |                                              |
|             |     | r |                                              |
|             |     | e |                                              |
|             |     | c |                                              |
|             |     | c |                                              |
|             |     | i |                                              |
|             |     | ó |                                              |
|             |     | n |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | b |                                              |
|             |     | u |                                              |
|             |     | g |                                              |
|             |     | s |                                              |
|             |     | , |                                              |
|             |     | D |                                              |
|             |     | o |                                              |
|             |     | c |                                              |
|             |     | u |                                              |
|             |     | m |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | a |                                              |
|             |     | c |                                              |
|             |     | i |                                              |
|             |     | ó |                                              |
|             |     | n |                                              |
|             |     | t |                                              |
|             |     | é |                                              |
|             |     | c |                                              |
|             |     | n |                                              |
|             |     | i |                                              |
|             |     | c |                                              |
|             |     | a |                                              |
|             |     | , |                                              |
|             |     | P |                                              |
|             |     | r |                                              |
|             |     | á |                                              |
|             |     | c |                                              |
|             |     | t |                                              |
|             |     | i |                                              |
|             |     | c |                                              |
|             |     | a |                                              |
|             |     | s |                                              |
|             |     | e |                                              |
|             |     | m |                                              |
|             |     | p |                                              |
|             |     | r |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | a |                                              |
|             |     | r |                                              |
|             |     | i |                                              |
|             |     | a |                                              |
|             |     | l |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | , |                                              |
|             |     | I |                                              |
|             |     | n |                                              |
|             |     | g |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | i |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | í |                                              |
|             |     | a |                                              |
|             |     | d |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | i |                                              |
|             |     | s |                                              |
|             |     | t |                                              |
|             |     | e |                                              |
|             |     | m |                                              |
|             |     | a |                                              |
|             |     | s |                                              |
|             |     | , |                                              |
|             |     | E |                                              |
|             |     | x |                                              |
|             |     | p |                                              |
|             |     | e |                                              |
|             |     | r |                                              |
|             |     | i |                                              |
|             |     | e |                                              |
|             |     | n |                                              |
|             |     | c |                                              |
|             |     | i |                                              |
|             |     | a |                                              |
|             |     | p |                                              |
|             |     | r |                                              |
|             |     | o |                                              |
|             |     | f |                                              |
|             |     | e |                                              |
|             |     | s |                                              |
|             |     | i |                                              |
|             |     | o |                                              |
|             |     | n |                                              |
|             |     | a |                                              |
|             |     | l |                                              |
+-------------+-----+---+----------------------------------------------+
| **V° B°     |     |   |                                              |
| DIRECTOR DE |     |   |                                              |
| TRABAJO DE  |     |   |                                              |
| GRADO**     |     |   |                                              |
+-------------+-----+---+----------------------------------------------+

![](media/image3.png){width="1.5731660104986878in"
height="0.308079615048119in"}

+--------------+-----+-----+------------------------------------------+
| ![Admintro   |     |     | **General summary of work of grade**     |
| log          |     |     |                                          |
| o](media/ima |     |     |                                          |
| ge2.png){wid |     |     |                                          |
| th="2.048611 |     |     |                                          |
| 111111111in" |     |     |                                          |
| height       |     |     |                                          |
| ="0.70833333 |     |     |                                          |
| 33333334in"} |     |     |                                          |
+==============+=====+=====+==========================================+
| **TITLE:**   |     | Ma  |                                          |
|              |     | int |                                          |
|              |     | ena |                                          |
|              |     | nce |                                          |
|              |     | of  |                                          |
|              |     | the |                                          |
|              |     | do  |                                          |
|              |     | cum |                                          |
|              |     | ent |                                          |
|              |     | m   |                                          |
|              |     | ana |                                          |
|              |     | gem |                                          |
|              |     | ent |                                          |
|              |     | sys |                                          |
|              |     | tem |                                          |
|              |     | in  |                                          |
|              |     | onl |                                          |
|              |     | ine |                                          |
|              |     | c   |                                          |
|              |     | ont |                                          |
|              |     | rol |                                          |
|              |     | S.A |                                          |
|              |     | .S. |                                          |
+--------------+-----+-----+------------------------------------------+
|              |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
| **           |     | J   |                                          |
| AUTHOR(S):** |     | uan |                                          |
|              |     | Seb |                                          |
|              |     | ast |                                          |
|              |     | ian |                                          |
|              |     | Ta  |                                          |
|              |     | raz |                                          |
|              |     | ona |                                          |
|              |     | Qu  |                                          |
|              |     | int |                                          |
|              |     | ero |                                          |
+--------------+-----+-----+------------------------------------------+
|              |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
| **FACULTY:** |     | Fa  |                                          |
|              |     | cul |                                          |
|              |     | tad |                                          |
|              |     | de  |                                          |
|              |     | I   |                                          |
|              |     | nge |                                          |
|              |     | nie |                                          |
|              |     | ría |                                          |
|              |     | de  |                                          |
|              |     | Si  |                                          |
|              |     | ste |                                          |
|              |     | mas |                                          |
|              |     | e   |                                          |
|              |     | In  |                                          |
|              |     | for |                                          |
|              |     | mát |                                          |
|              |     | ica |                                          |
+--------------+-----+-----+------------------------------------------+
|              |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
| *            |     | Yur |                                          |
| *DIRECTOR:** |     | ley |                                          |
|              |     | Es  |                                          |
|              |     | tef |                                          |
|              |     | any |                                          |
|              |     | Ru  |                                          |
|              |     | eda |                                          |
|              |     | Rom |                                          |
|              |     | ero |                                          |
+--------------+-----+-----+------------------------------------------+
|              |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
|              |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
| **ABSTRACT** |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
| This final   |     |     |                                          |
| report       |     |     |                                          |
| presents the |     |     |                                          |
| results      |     |     |                                          |
| obtained     |     |     |                                          |
| during the   |     |     |                                          |
| development  |     |     |                                          |
| of the       |     |     |                                          |
| professional |     |     |                                          |
| internship   |     |     |                                          |
| carried out  |     |     |                                          |
| at the       |     |     |                                          |
| company      |     |     |                                          |
| Control      |     |     |                                          |
| Online       |     |     |                                          |
| I            |     |     |                                          |
| nternational |     |     |                                          |
| S.A.S.,      |     |     |                                          |
| focused on   |     |     |                                          |
| the          |     |     |                                          |
| maintenance, |     |     |                                          |
| improvement, |     |     |                                          |
| and          |     |     |                                          |
| im           |     |     |                                          |
| plementation |     |     |                                          |
| of new       |     |     |                                          |
| features in  |     |     |                                          |
| the          |     |     |                                          |
| ControlDoc   |     |     |                                          |
| document     |     |     |                                          |
| management   |     |     |                                          |
| system.      |     |     |                                          |
| Throughout   |     |     |                                          |
| the process, |     |     |                                          |
| tasks were   |     |     |                                          |
| performed    |     |     |                                          |
| related to   |     |     |                                          |
| bug fixing,  |     |     |                                          |
| development  |     |     |                                          |
| of           |     |     |                                          |
| functional   |     |     |                                          |
| modules, and |     |     |                                          |
| integration  |     |     |                                          |
| of           |     |     |                                          |
| components   |     |     |                                          |
| for the      |     |     |                                          |
| management   |     |     |                                          |
| of manuals,  |     |     |                                          |
| tutorials,   |     |     |                                          |
| and document |     |     |                                          |
| deadlines.   |     |     |                                          |
| The workflow |     |     |                                          |
| was          |     |     |                                          |
| organized    |     |     |                                          |
| using a      |     |     |                                          |
| methodology  |     |     |                                          |
| based on the |     |     |                                          |
| Scrum        |     |     |                                          |
| framework,   |     |     |                                          |
| allowing     |     |     |                                          |
| iterative    |     |     |                                          |
| planning     |     |     |                                          |
| through      |     |     |                                          |
| Sprints and  |     |     |                                          |
| progressive  |     |     |                                          |
| validation   |     |     |                                          |
| of the       |     |     |                                          |
| software     |     |     |                                          |
| through QA   |     |     |                                          |
| and UAT      |     |     |                                          |
| testing.     |     |     |                                          |
|              |     |     |                                          |
| This         |     |     |                                          |
| document     |     |     |                                          |
| outlines the |     |     |                                          |
| or           |     |     |                                          |
| ganizational |     |     |                                          |
| and          |     |     |                                          |
| technical    |     |     |                                          |
| context in   |     |     |                                          |
| which the    |     |     |                                          |
| activities   |     |     |                                          |
| were carried |     |     |                                          |
| out, as well |     |     |                                          |
| as the       |     |     |                                          |
| impact of    |     |     |                                          |
| the results  |     |     |                                          |
| on the       |     |     |                                          |
| production   |     |     |                                          |
| environment. |     |     |                                          |
|              |     |     |                                          |
| Finally, the |     |     |                                          |
| report       |     |     |                                          |
| highlights   |     |     |                                          |
| the          |     |     |                                          |
| evolution of |     |     |                                          |
| the learning |     |     |                                          |
| process and  |     |     |                                          |
| the          |     |     |                                          |
| s            |     |     |                                          |
| trengthening |     |     |                                          |
| of           |     |     |                                          |
| professional |     |     |                                          |
| competencies |     |     |                                          |
| in the field |     |     |                                          |
| of systems   |     |     |                                          |
| and computer |     |     |                                          |
| engineering. |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
|              |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
| *            |     |     |                                          |
| *KEYWORDS:** |     |     |                                          |
+--------------+-----+-----+------------------------------------------+
|              |     | Co  |                                          |
|              |     | ntr |                                          |
|              |     | olD |                                          |
|              |     | oc, |                                          |
|              |     | G   |                                          |
|              |     | est |                                          |
|              |     | ión |                                          |
|              |     | do  |                                          |
|              |     | cum |                                          |
|              |     | ent |                                          |
|              |     | al, |                                          |
|              |     | S   |                                          |
|              |     | ist |                                          |
|              |     | ema |                                          |
|              |     | de  |                                          |
|              |     | g   |                                          |
|              |     | est |                                          |
|              |     | ión |                                          |
|              |     | de  |                                          |
|              |     | d   |                                          |
|              |     | ocu |                                          |
|              |     | men |                                          |
|              |     | tos |                                          |
|              |     | e   |                                          |
|              |     | lec |                                          |
|              |     | tró |                                          |
|              |     | nic |                                          |
|              |     | os, |                                          |
|              |     | SGD |                                          |
|              |     | EA, |                                          |
|              |     | A   |                                          |
|              |     | SP. |                                          |
|              |     | NET |                                          |
|              |     | Co  |                                          |
|              |     | re, |                                          |
|              |     | B   |                                          |
|              |     | laz |                                          |
|              |     | or, |                                          |
|              |     | Ent |                                          |
|              |     | ity |                                          |
|              |     | Fra |                                          |
|              |     | mew |                                          |
|              |     | ork |                                          |
|              |     | Co  |                                          |
|              |     | re, |                                          |
|              |     | Arq |                                          |
|              |     | uit |                                          |
|              |     | ect |                                          |
|              |     | ura |                                          |
|              |     | l   |                                          |
|              |     | imp |                                          |
|              |     | ia, |                                          |
|              |     | Dom |                                          |
|              |     | ain |                                          |
|              |     | Dri |                                          |
|              |     | ven |                                          |
|              |     | D   |                                          |
|              |     | esi |                                          |
|              |     | gn, |                                          |
|              |     | D   |                                          |
|              |     | DD, |                                          |
|              |     | Mic |                                          |
|              |     | ros |                                          |
|              |     | erv |                                          |
|              |     | ici |                                          |
|              |     | os, |                                          |
|              |     | API |                                          |
|              |     | Ga  |                                          |
|              |     | tew |                                          |
|              |     | ay, |                                          |
|              |     | Az  |                                          |
|              |     | ure |                                          |
|              |     | Clo |                                          |
|              |     | ud, |                                          |
|              |     | Scr |                                          |
|              |     | um, |                                          |
|              |     | S   |                                          |
|              |     | pri |                                          |
|              |     | nt, |                                          |
|              |     | QA, |                                          |
|              |     | U   |                                          |
|              |     | AT, |                                          |
|              |     | Pat |                                          |
|              |     | rón |                                          |
|              |     | de  |                                          |
|              |     | dis |                                          |
|              |     | eño |                                          |
|              |     | Rep |                                          |
|              |     | osi |                                          |
|              |     | tor |                                          |
|              |     | io, |                                          |
|              |     | T   |                                          |
|              |     | ele |                                          |
|              |     | rik |                                          |
|              |     | UI  |                                          |
|              |     | for |                                          |
|              |     | B   |                                          |
|              |     | laz |                                          |
|              |     | or, |                                          |
|              |     | Mic |                                          |
|              |     | ros |                                          |
|              |     | oft |                                          |
|              |     | Pl  |                                          |
|              |     | ann |                                          |
|              |     | er, |                                          |
|              |     | G   |                                          |
|              |     | itH |                                          |
|              |     | ub, |                                          |
|              |     | Ku  |                                          |
|              |     | ber |                                          |
|              |     | net |                                          |
|              |     | es, |                                          |
|              |     | C   |                                          |
|              |     | ont |                                          |
|              |     | rol |                                          |
|              |     | Onl |                                          |
|              |     | ine |                                          |
|              |     | I   |                                          |
|              |     | nte |                                          |
|              |     | rna |                                          |
|              |     | tio |                                          |
|              |     | nal |                                          |
|              |     | S   |                                          |
|              |     | .A. |                                          |
|              |     | S., |                                          |
|              |     | M   |                                          |
|              |     | ant |                                          |
|              |     | eni |                                          |
|              |     | mie |                                          |
|              |     | nto |                                          |
|              |     | de  |                                          |
|              |     | sof |                                          |
|              |     | twa |                                          |
|              |     | re, |                                          |
|              |     | D   |                                          |
|              |     | esa |                                          |
|              |     | rro |                                          |
|              |     | llo |                                          |
|              |     | de  |                                          |
|              |     | f   |                                          |
|              |     | unc |                                          |
|              |     | ion |                                          |
|              |     | ali |                                          |
|              |     | dad |                                          |
|              |     | es, |                                          |
|              |     | C   |                                          |
|              |     | orr |                                          |
|              |     | ecc |                                          |
|              |     | ión |                                          |
|              |     | de  |                                          |
|              |     | bu  |                                          |
|              |     | gs, |                                          |
|              |     | D   |                                          |
|              |     | ocu |                                          |
|              |     | men |                                          |
|              |     | tac |                                          |
|              |     | ión |                                          |
|              |     | té  |                                          |
|              |     | cni |                                          |
|              |     | ca, |                                          |
|              |     | Prá |                                          |
|              |     | cti |                                          |
|              |     | cas |                                          |
|              |     | em  |                                          |
|              |     | pre |                                          |
|              |     | sar |                                          |
|              |     | ial |                                          |
|              |     | es, |                                          |
|              |     | I   |                                          |
|              |     | nge |                                          |
|              |     | nie |                                          |
|              |     | ría |                                          |
|              |     | de  |                                          |
|              |     | sis |                                          |
|              |     | tem |                                          |
|              |     | as, |                                          |
|              |     | Ex  |                                          |
|              |     | per |                                          |
|              |     | ien |                                          |
|              |     | cia |                                          |
|              |     | pr  |                                          |
|              |     | ofe |                                          |
|              |     | sio |                                          |
|              |     | nal |                                          |
+--------------+-----+-----+------------------------------------------+
| **V° B°      |     |     |                                          |
| DIRECTOR OF  |     |     |                                          |
| GRADUATE     |     |     |                                          |
| WORK**       |     |     |                                          |
+--------------+-----+-----+------------------------------------------+

![](media/image3.png){width="1.5731660104986878in"
height="0.308079615048119in"}

# Introducción

Este informe final tiene como objetivo documentar el desarrollo completo
de las prácticas empresariales, presentando los resultados alcanzados
durante todo el proceso, así como las experiencias obtenidas en el
entorno productivo de Control Online International, una empresa dedicada
al desarrollo de software, con un enfoque especial en la evolución y
crecimiento de su producto principal, ControlDoc.

A lo largo de este documento, se detalla la experiencia adquirida en
dicho entorno, enfocada en las tareas de mantenimiento, mejora e
implementación de nuevas funcionalidades dentro del sistema de gestión
documental ControlDoc. Se describe el impacto de las actividades
desarrolladas tanto a nivel técnico como en el funcionamiento general
del producto.

Con el fin de contextualizar el rol desempeñado por el estudiante, se
presenta primero la situación problemática, el producto sobre el cual se
desarrollaron las prácticas y su propuesta de valor. Esto permite
comprender el flujo de trabajo y los resultados obtenidos durante el
proceso formativo.

También se establecen los objetivos generales y específicos que guiaron
el proceso de aprendizaje y ayudaron a definir el alcance de las
actividades dentro de la empresa. Esto permite visualizar de manera más
clara las funciones y responsabilidades dentro del equipo de trabajo.

Posteriormente, se presentan los antecedentes, donde a partir de una
revisión de la literatura, se analizan soluciones previas a la
problemática planteada. Luego, en la justificación, se explican las
razones por las cuales se llevaron a cabo estas prácticas y los
beneficios que aportaron tanto al crecimiento profesional como al
desarrollo de la organización.

En el marco referencial, se abordan los conceptos clave, tecnologías y
herramientas que formaron parte del trabajo diario en Control Online
International. Esta sección sirve como base para comprender el contexto
técnico en el que se desarrollaron las actividades.

Además, se describe la metodología utilizada en la empresa para el
desarrollo de software, la cual guió las actividades y procesos de
aprendizaje. Se comparte un ejemplo del flujo de trabajo dentro de la
empresa para ilustrar cómo se organizan y gestionan los proyectos en un
entorno profesional real.

Más adelante, se presenta un cronograma con las actividades realizadas,
lo que permite visualizar de manera estructurada el progreso alcanzado.

Finalmente, se presentan los resultados obtenidos durante todo el
periodo de prácticas, los cuales evidencian el impacto real del trabajo
realizado en el sistema ControlDoc. Estos resultados reflejan tanto el
cumplimiento de los objetivos planteados como el fortalecimiento de las
competencias técnicas y profesionales del estudiante dentro de un
entorno de desarrollo empresarial.

# Generalidades de la empresa

La experiencia de prácticas empresariales se desarrolla en Control
Online International, una empresa que lleva más de 25 años transformando
la manera en que las organizaciones manejan sus documentos. Fundada en
febrero de 1998, esta compañía se especializa en crear software de
gestión documental que realmente hace la diferencia para sus clientes.

Lo que resulta destacable es cómo han logrado posicionarse no solo en
Colombia, sino también en otros países latinoamericanos. Su enfoque
principal está en ayudar a empresas colombianas que enfrentan el desafío
diario de organizar enormes cantidades de información documental
(Control Online International, 2023).

Una característica que destaca es la cercanía con los clientes. No es
solo una relación comercial típica, sino que trabajan de la mano con
cada cliente, escuchando sus necesidades cuando requieren nuevas
funcionalidades o cuando surgen problemas técnicos. Esta dinámica
colaborativa fue observada directamente durante el desarrollo de las
prácticas.

La empresa opera desde dos sedes: la principal en Bogotá (Avenida El
Dorado No. 69-63) que maneja la parte administrativa, y la sede de
desarrollo en Floridablanca donde el estudiante desarrolló las
prácticas. Esta segunda sede está ubicada en el Anillo Vial, Ecoparque
Empresarial Natura, un espacio dedicado exclusivamente al desarrollo del
software donde el estudiante tuvo la oportunidad de participar en el
desarrollo de software empresarial.

# Estructura organizacional

La empresa Control Online International dispone de una estructura
organizacional de tipo jerárquico, distribuida en distintos niveles de
autoridad y responsabilidad. Esta organización está representada
gráficamente en el siguiente diagrama, construido a partir de la
información suministrada por el área de Gestión Humana.

La estructura evidencia una segmentación clara por niveles funcionales,
donde las decisiones estratégicas y operativas se delegan
progresivamente desde los niveles más altos hacia los operativos. En la
parte superior se encuentra la Presidencia, como instancia máxima de
decisión dentro de la organización, acompañada por la Vicepresidencia
Financiera y Administrativa y comités de apoyo como el Comité de
Calidad.

A medida que se desciende en la estructura, se encuentran niveles
administrativos, técnicos y auxiliares que responden funcionalmente a
áreas como gestión humana, compras, calidad, y proyectos. Esta
disposición permite una coordinación eficiente entre los procesos de
dirección, soporte y ejecución operativa.

Durante el desarrollo de las prácticas empresariales, el estudiante
participó dentro del nivel técnico de la organización, al estar
vinculado bajo la modalidad de "aprendiz". Este nivel corresponde a
funciones operativas de apoyo en áreas específicas, acorde con su etapa
formativa previa a la obtención del título profesional.

***\
***

[]{#_Toc205131363 .anchor}**Figura 1**\
Estructura organizacional de Control Online International.

![](media/image4.jpeg){width="4.8984962817147855in"
height="3.691666666666667in"}

*Nota.* Adaptada de Control Online International S.A.S.

# Planteamiento del problema

En la era digital, la información es uno de los activos más valiosos
para las empresas. Cada dato, por pequeño que sea, puede marcar la
diferencia en la toma de decisiones estratégicas. Sin embargo, a medida
que las organizaciones crecen y generan grandes volúmenes de
información, gestionar y almacenar estos datos se convierte en un
desafío cada vez más complejo.

Muchas empresas aún dependen de procesos manuales para organizar su
documentación, lo que provoca retrasos, pérdida de información,
dificultades en la consulta y problemas de trazabilidad. La falta de
digitalización y estandarización no solo impacta la eficiencia
operativa, sino que también incrementa los costos y puede llevar al
incumplimiento de normativas. Además, sin un mantenimiento adecuado, los
sistemas de gestión documental pueden quedar obsoletos, volviéndose
ineficaces y limitando la capacidad de adaptación de las empresas a
nuevos desafíos.

Para enfrentar estos problemas, cada vez más organizaciones recurren a
soluciones tecnológicas especializadas. Entre las alternativas de
software de gestión documental en Colombia, ControlDoc se ha consolidado
como una herramienta clave en la administración documental. Según su
página web oficial, ControlDoc es \"un sistema de gestión de documentos
electrónicos de archivo (SGDEA) especializado en la administración de la
producción documental de las Entidades, garantizando la conservación de
la evidencia documental que da cumplimiento a las funciones que le han
sido asignadas\" (Control Online International, 2023). Su desarrollo y
mantenimiento continuo garantizan mejoras en el rendimiento, reducción
de errores y optimización de los procesos empresariales.

¿Cómo el mantenimiento y desarrollo continuo de nuevas funcionalidades
en el software ControlDoc puede contribuir a la optimización de los
procesos de gestión documental empresarial y mejorar la experiencia de
usuario en las organizaciones que lo implementan?

# Objetivos

## Objetivo general

Contribuir al desarrollo y mantenimiento del software de gestión
documental ControlDoc, mediante la optimización de sus procesos y
funcionalidades, aplicando principios de diseño de software, junto con
el marco de trabajo SCRUM, facilitando que el producto evolucione según
las necesidades del mercado y los requerimientos de los clientes.

## Objetivos específicos

-   Implementar nuevas funcionalidades en ControlDoc, utilizando
    tecnologías del stack .NET y el marco de trabajo SCRUM para mejorar
    las capacidades del software y facilitar su crecimiento.

-   Realizar mantenimiento continuo del software mediante la corrección
    de errores, actualizaciones y optimización de funciones, aplicando
    principios de arquitectura limpia basada en microservicios, patrones
    de diseño para fortalecer la estabilidad y escalabilidad del
    sistema.

-   Desarrollar competencias en la aplicación práctica del marco de
    trabajo SCRUM y principios de ingeniería de software, contribuyendo
    a la optimización de los procesos de desarrollo y facilitando la
    evolución del producto ante los cambios del mercado.

# Justificación

El desarrollo y mantenimiento continuo de sistemas de gestión documental
como ControlDoc representa una necesidad crítica para las organizaciones
modernas que buscan optimizar sus procesos administrativos y cumplir con
las regulaciones normativas vigentes. En un contexto empresarial donde
la información se ha convertido en un activo estratégico, la evolución
constante del software es fundamental para mantener la competitividad y
eficiencia operativa.

La participación en el mantenimiento y desarrollo de ControlDoc
contribuye significativamente al fortalecimiento de las capacidades
tecnológicas de Control Online International, permitiendo que la empresa
se mantenga a la vanguardia en el sector de sistemas de gestión
documental. Esta labor impacta directamente en la calidad del producto
final, facilitando mejoras en la experiencia del usuario, optimización
de procesos y reducción de errores operativos.

Desde la perspectiva del sector empresarial, el perfeccionamiento
continuo de herramientas como ControlDoc facilita que las organizaciones
puedan adaptarse a los cambios normativos, mejorar sus procesos de
trazabilidad documental y reducir los costos operativos asociados a la
gestión manual de documentos. Esto se traduce en un impacto positivo en
la productividad y competitividad de las empresas usuarias del sistema.

Adicionalmente, esta experiencia permite la aplicación práctica de
conceptos de ingeniería de software en un entorno real, contribuyendo al
desarrollo de soluciones tecnológicas que respondan a las necesidades
específicas del mercado colombiano y fortalezcan la posición de la
empresa en el sector de gestión documental electrónica.

# Marco conceptual

Con el fin de una mejor comprensión del presente informe de avance, es
necesario hacer una revisión a varios conceptos y sus respectivos
significados en el presente contexto, entre ellos están:

## Sistema de Gestión de Documentos Electrónicos (SGDEA)

Para empezar, debe tenerse en claro el tipo de software sobre el cual se
está trabajando. En el caso de ControlDoc se trata de un sistema de
gestión documental de documentos electrónicos (SGDEA) (Control Online
International, 2023). A grandes rasgos, se puede definir un SGDEA como
uno o varios sistemas de información que tienen el objetivo de gestionar
documentos electrónicos (MinCiencias, s.f.). Los SGDEA por lo general
tienen como principal oferta de valor el facilitar y optimizar los
procesos de gestión empresariales.

## Documento electrónico

Para comprender mejor el concepto previamente expuesto de SGDEA es
conveniente hacer un análisis sobre el concepto de documento
electrónico. Cuando se habla de documento electrónico se hace referencia
a cualquier documento creado y gestionado en un medio digital. Los
documentos electrónicos comparten características esenciales con los
documentos físicos, no obstante, a pesar de tener el mismo propósito de
almacenar información, los documentos electrónicos tienen muchos
beneficios frente a los documentos físicos, los cuales están
relacionados con la facilidad de almacenamiento, el preservamiento de la
integridad y rapidez a la hora de gestionarlos (Herranz & de Hontanares,
2010). Lo que permite a muchas empresas poder incrementar su
productividad en términos de gestión de documentos.

## Arquitectura limpia

Para el desarrollo del software ControlDoc se implementó una
arquitectura limpia, específicamente Domain Driven Design. Cuando se
habla de arquitectura limpia se hace referencia a una filosofía de
diseño de software, la cual tiene como propósito principal separar la
lógica empresarial y los detalles técnicos a la hora de la
implementación, de forma que se preserve la correcta separación de
responsabilidades (Martin, 2017).

## Web Api

Para una de las partes fundamentales de una arquitectura limpia está la
capa de presentación o mejor conocida como web API. Esta es, por así
decirlo, la puerta principal de una aplicación, en el sentido de que
permite la constante entrada y salida de información, esto mediante
peticiones y respuestas, las cuales se pueden recibir y enviar por
distintos métodos del protocolo HTTP. No obstante, más allá de las
solicitudes y respuestas de información, una web API se puede definir
más como una interfaz de métodos, la cual tiene como función brindar
métodos para operar sobre uno o varios microservicios, de manera que las
webs API cumplen una gran función a la hora de comunicar las distintas
partes de un software no monolítico (Maleshkova, Pedrinaci, & Domingue,
2010).

## Base de datos

Como es bien conocido, la información es la piedra angular y razón de
cualquier software, en especial del software destinado al almacenamiento
de la información, como sería el caso de ControlDoc al ser un gestor de
documentos electrónicos. Para el almacenamiento de la información en un
software, una herramienta fundamental es la base de datos. Una base de
datos, como su nombre indica, se encarga de recopilar información,
haciéndolo mediante medios electrónicos, lo que permite operar mediante
medios electrónicos (Beynon-Davies, 2018).

# Marco tecnológico

Después de realizar un análisis a los distintos conceptos que son
relevantes durante el desarrollo de las prácticas y el presente informe,
se procede a hacer una revisión a las tecnologías que se utilizan en el
desarrollo de las prácticas en Control Online International.

## Domain Driven Desing

Para la implementación del software ControlDoc, se utiliza una
arquitectura basada en Domain Driven Design, arquitectura la cual se
compone por 4 capas principales: una capa de presentación encargada del
API, una capa de aplicación donde se gestionan los casos de uso, una
capa de dominio encargada de contener el modelo de dominio, las
entidades y las reglas de negocio y finalmente una capa de
infraestructura, la cual cubre todos los detalles técnicos como acceso a
bases de datos, servicios externos y otros (Evans, 2003).

Cuando se habla de Domain Driven Design se hace referencia a una
práctica de desarrollo que, como su nombre indica, se centra en el
dominio, de manera que las entidades y las reglas de negocio sean más
fáciles de desarrollar a medida que crece el software (Evans, 2003).

## Patrón de diseño repositorio

En Domain Driven Design y en la mayoría de las arquitecturas limpias es
muy común encontrar el patrón repositorio para acceder a los datos,
incluso es muy común que muchos desarrolladores lo usen sin siquiera
conocer sobre patrones de diseño.

El patrón repositorio, al igual que otros patrones de diseño, busca
mejorar la calidad del software mediante la solución de problemas
comunes a la hora de desarrollar (Campo, 2009). Este patrón de diseño en
específico busca aislar la lógica de acceso a datos y asignarles un
modelo/entidad que defina la estructura de estos, facilitando la
obtención de datos desde una capa de casos de uso o aplicación, de
manera que a la hora de consultar información se puede llamar a un
método del repositorio de una entidad, delegando la responsabilidad de
la implementación de los métodos a la capa de infraestructura y no a la
de aplicación. Además, como se mencionó anteriormente, permite hacer
operaciones sobre un modelo, logrando así una mayor transparencia en
cuanto a las operaciones con datos (Heiland, Hauser, & Bogner, 2023).

## ASP.NET Core

Para realizar las webs API en ControlDoc se optó por usar como framework
ASP.NET Core. Esta decisión se tomó principalmente por la estabilidad
que permite dicho framework, además de por su gran compatibilidad con
Microsoft (siendo ASP.NET Core desarrollado por esta empresa, como una
mejor versión de su antecesor ASP.NET).

En esencia, ASP.NET Core se trata de un framework modular de código
abierto y multiplataforma, principalmente orientado para aplicaciones
que interactúan con Internet, debido a que escucha peticiones HTTP y se
las pasa a la aplicación como un conjunto de características (como por
ejemplo un archivo JSON que guarda en varios campos las características
de la solicitud) (Lock, 2023).

## Blazor

Para realizar el front end de ControlDoc se optó por usar Blazor. Esta
decisión se tomó principalmente debido a que usa C#, al igual que
ASP.NET Core, por lo que a la hora de desarrollar se puede dejar en
segundo plano el tradicional JavaScript y usar solo tecnologías del
stack de .NET.

Blazor es un framework de código abierto orientado al desarrollo web, el
cual principalmente usa HTML, C# y CSS, no obstante, también permite
invocar librerías y scripts de JavaScript desde C#. Blazor está
orientado al desarrollo por componentes, de forma que tanto la
estructura, funcionalidad y apariencia pueden ser separadas en distintas
clases, además admite incrustar código de C# en HTML, de forma que se
pueden llamar funciones de C# para modificar la estructura de la página
mediante bucles y condicionales.

Adicionalmente, Blazor puede alojar aplicaciones de 2 maneras
diferentes: desde el servidor o desde el cliente. Por un lado, el
alojamiento del lado del servidor funciona de manera que todo el código
funciona desde el lado del servidor y se actualiza la interfaz del
cliente mediante SignalR, el cual sirve como puente entre la aplicación
del cliente y el servidor, y mediante el cual el cliente envía
peticiones al servidor para posteriormente enviar actualizaciones al
DOM. Este modelo de alojamiento tiene la principal ventaja de que el
cliente no debe descargar los archivos, no obstante, al realizarse la
carga del lado del servidor, el rendimiento de la aplicación puede verse
afectado por la latencia y los recursos en el servidor.

Por otro lado, cuando la aplicación web se aloja en el cliente, la
aplicación usa WebAssembly, de manera que el cliente debe descargar todo
el código para posteriormente ejecutarse. Esto tiene la ventaja de que
no se depende de un servidor, no obstante, el tiempo de descarga inicial
de la aplicación puede llegar a ser mucho mayor (Himschoot, 2020).

## Entity Framework Core

Para el desarrollo de ControlDoc fundamentado en el stack de .NET, a la
hora del acceso y modelado de bases de datos se optó por usar Entity
Framework Core. Esta decisión se tomó porque permite usar código en
lugar de SQL, además de que se puede usar con LINQ lo que permite la
fácil consulta de datos en un patrón repositorio, lo que a su vez
permite realizar el modelado del dominio, colaborando con la
construcción de la arquitectura basada en Domain Driven Design, la cual
gira en torno a la creación de un dominio modelado por entidades.

Entity Framework Core se trata de una librería creada por Microsoft la
cual, como se mencionó antes, permite el fácil acceso a base de datos,
además de permitir el modelado por entidades mediante dicha librería.
Dicho proceso de modelado puede comenzar de 2 formas: Code First o
Database First.

En el caso de Code First se comenzaría con la creación de las entidades,
relaciones y configuraciones en código, para posteriormente generar una
migración, y a partir de esta, crear una base de datos con sus
respectivas tablas y entidades. Por el caso contrario, si se desea
comenzar el modelado mediante Database First, se comenzaría el modelado
a partir de una base de datos ya establecida y a partir de esta, Entity
Framework permite generar el código de las respectivas clases,
relaciones y configuraciones.

Una vez se tienen las entidades tanto en el código como en la base de
datos, se puede comenzar a operar sobre la base de datos sin necesidad
de usar SQL, esto de una manera fácil y segura. A grandes rasgos, la
utilidad de Entity Framework Core recae en la posibilidad de gestionar
una base de datos desde el código, permitiendo así tener un mejor
control del dominio en una arquitectura basada en el dominio como Domain
Driven Design (Entity Framework, 2021).

## GitHub

Durante el desarrollo del sistema ControlDoc, se utiliza la plataforma
GitHub como sistema de control de versiones distribuido. Cada
desarrollador trabaja sobre una rama independiente, en la cual
implementa las funcionalidades o correcciones asignadas durante el
Sprint.

Una vez finalizadas y validadas las tareas, estas ramas son integradas
(mediante merge) a una rama principal del repositorio.

Debido a la arquitectura basada en microservicios, cada componente del
sistema cuenta con su repositorio independiente, lo que permite mantener
una estructura modular, escalar funcionalidades de manera aislada y
garantizar trazabilidad por servicio. El frontend de la aplicación
también se encuentra desacoplado y alojado en un repositorio separado,
facilitando su mantenimiento y evolución.

GitHub es una plataforma colaborativa que se basa en Git, permitiendo a
los equipos gestionar versiones de código, controlar cambios, y realizar
integraciones continuas de manera eficiente (Chacon & Straub, 2014).

## Telerik

Para el desarrollo de la interfaz de usuario, se emplea la biblioteca
Telerik UI for Blazor, la cual proporciona un conjunto de componentes
reutilizables como formularios dinámicos, tablas, selectores, ventanas
modales, entre otros.

El uso de Telerik permite agilizar la construcción de interfaces
complejas, garantizando uniformidad visual y reduciendo el esfuerzo
requerido para desarrollar componentes personalizados desde cero. Esta
herramienta ha sido fundamental para mantener estándares visuales y
mejorar la experiencia de usuario.

Telerik UI for Blazor es una suite de componentes visuales desarrollada
por Progress Software, diseñada para integrarse con aplicaciones .NET,
permitiendo construir interfaces modernas y responsivas con menor
esfuerzo de desarrollo (Progress Software Corporation, 2024).

# Metodología

El presente proyecto de prácticas se desarrolla en un entorno
empresarial orientado al mantenimiento y evolución continua de sistemas
de información. En este contexto, la empresa Control Online
International implementa el marco de trabajo ágil Scrum para gestionar
el ciclo de vida del software ControlDoc, promoviendo la adaptabilidad,
la colaboración continua y la entrega incremental de valor (Brayitaan,
2024).

Scrum ha demostrado ser una metodología efectiva para proyectos donde
los requerimientos pueden cambiar con frecuencia, como es el caso del
sistema ControlDoc, el cual está en constante evolución de acuerdo con
las necesidades específicas de los clientes. Por medio de iteraciones de
trabajo denominadas Sprints, el equipo puede planificar, ejecutar,
validar y entregar funcionalidades de forma controlada y predecible.

## Roles y estructura del equipo

El equipo de desarrollo se organiza bajo una estructura definida por
roles clave, donde cada integrante cumple una función específica en el
cumplimiento de los objetivos del Sprint:

-   **Product Owner:** encargado de mantener comunicación directa con el
    cliente, interpretar requerimientos funcionales y priorizar
    historias de usuario.

-   **Scrum Master:** responsable de facilitar el proceso ágil, eliminar
    impedimentos, distribuir tareas y coordinar las reuniones diarias.

-   **Equipo de desarrollo:** conformado por programadores encargados de
    implementar nuevas funcionalidades, corregir errores y mantener el
    sistema.

-   **Equipo de QA (Quality Assurance):** encargado de realizar pruebas
    técnicas sobre las funcionalidades desarrolladas para validar su
    correcto funcionamiento y asegurar la calidad del producto.

-   **Equipo de soporte/UAT (User Acceptance Testing):** responsable de
    validar la funcionalidad implementada desde la perspectiva del
    usuario final, previo al despliegue en ambiente de producción.

## Ciclo de desarrollo en ControlDoc

El proceso de desarrollo se organiza en ciclos de trabajo quincenales
(Sprints), cada uno compuesto por las siguientes fases:

### Levantamiento y documentación de requerimientos

Las solicitudes de nuevas funcionalidades, mejoras o correcciones de
errores son registradas por los equipos de soporte o directamente por
los clientes. Posteriormente, estas se documentan como historias de
usuario dentro de Microsoft Planner, con sus respectivos criterios de
aceptación.

### Planificación del Sprint (Sprint Planning)

Las historias de usuario son priorizadas por el Product Owner, quien las
presenta al equipo de desarrollo a través del Scrum Master. Las tareas
se asignan teniendo en cuenta la experiencia previa de los
desarrolladores y la complejidad técnica estimada.

### Ejecución y reuniones diarias (Daily Scrum)

Durante el desarrollo del Sprint, se llevan a cabo reuniones diarias
donde se reporta el estado de las tareas, se identifican posibles
bloqueos y se ajusta la planificación cuando es necesario.

La Figura 1 resume gráficamente los elementos que componen el marco
Scrum en ControlDoc.

### Pruebas QA (Quality Assurance)

Una vez finalizadas las tareas, se procede a la ejecución de pruebas
técnicas en el entorno de calidad, con el fin de validar que las
funcionalidades desarrolladas cumplan con los criterios definidos, sin
generar errores colaterales.

### Pruebas UAT (User Acceptance Testing)

Las funcionalidades que superan las pruebas QA son trasladadas al
entorno de aceptación, donde son evaluadas por el equipo de soporte o
directamente por los usuarios finales. La validación en este entorno es
esencial para asegurar que el sistema cumple con las expectativas del
cliente, como se ilustra en la Figura 2.

### Cierre del Sprint y liberación a producción

Al finalizar el Sprint, se realiza una revisión general (Sprint Review)
donde se presentan los avances alcanzados. Posteriormente, las
funcionalidades aprobadas son liberadas en el entorno productivo.
Finalmente, en la reunión de retrospectiva, se identifican oportunidades
de mejora en el proceso.

## Herramientas y gestión del flujo de trabajo

Para la gestión de tareas, se utiliza Microsoft Planner de Teams, donde
se clasifican y priorizan actividades por tipo (bugs, mejoras,
funcionalidades nuevas). Esta herramienta permite mantener trazabilidad
del trabajo asignado, facilitar la colaboración entre equipos y
coordinar entregas de manera estructurada.

Además, se emplean herramientas de control de versiones como Git, así
como entornos diferenciados para pruebas QA, UAT y producción, lo cual
garantiza una transición ordenada desde el desarrollo hasta la entrega
final.

## Representación gráfica del proceso metodológico

A continuación, se presentan representaciones visuales que ilustran la
estructura metodológica utilizada en Control Online International para
el desarrollo de ControlDoc:

[]{#_Toc205131364 .anchor}**Figura 2**\
Marco de trabajo Scrum aplicado

![Diagrama El contenido generado por IA puede ser
incorrecto.](media/image5.png){width="5.217361111111111in"
height="2.4in"}

*Nota.* Adaptada de (Brayitaan, 2024).

Representa los eventos, artefactos y roles que conforman el marco Scrum,
los cuales permiten organizar el trabajo en ciclos iterativos de valor.

[]{#_Toc205131365 .anchor}**Figura 3**\
Flujo de trabajo desde requerimiento hasta producción

![Diagrama El contenido generado por IA puede ser
incorrecto.](media/image6.png){width="5.580232939632546in"
height="3.6416666666666666in"}

Describe la secuencia lógica de pasos que sigue una funcionalidad: desde
la recolección de requerimientos hasta la validación final por el
cliente y la liberación a producción.

[]{#_Toc205131366 .anchor}**Figura 4**\
Vista de Microsoft Planner utilizada en la gestión de tareas

![Interfaz de usuario gráfica, Aplicación, Teams El contenido generado
por IA puede ser
incorrecto.](media/image7.png){width="4.770324803149606in"
height="2.9065616797900264in"}

*Nota.* Adaptada de Control Online International S.A.S.

Muestra cómo se organizan y clasifican las tareas dentro del entorno de
Microsoft Planner, permitiendo visibilidad, seguimiento y control del
trabajo por parte de todos los actores.

# Cronograma de actividades

El tiempo destinado a la realización de las prácticas empresariales es
de 6 meses. El cronograma de actividades esta divido en las siguientes
fases:[]{#_bookmark24 .anchor}

**Tabla 1**\
Cronograma ejecución actividades propuestas

  -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  **Actividades**   **Mes 1**                                   **Mes 2**                                   **Mes 3**                                   **Mes 4**                                   **Mes 5**                                   **Mes 6**                        
  ----------------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ---------- ----------
                    **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana   **Semana
                    1**        2**        3**        4**        5**        6**        7**        8**        9**        10**       11**       12**       13**       14**       15**       16**       17**       18**       19**       20**       21**       22**       23**       24**

  Inducción                                                                                                                                                                                                                                                                      

  Introducción al                                                                                                                                                                                                                                                                
  equipo de scrum                                                                                                                                                                                                                                                                

  Introducción al                                                                                                                                                                                                                                                                
  desarrollo                                                                                                                                                                                                                                                                     
  Frontend                                                                                                                                                                                                                                                                       

  Introducción al                                                                                                                                                                                                                                                                
  desarrollo                                                                                                                                                                                                                                                                     
  backend                                                                                                                                                                                                                                                                        

  Desarrollo de                                                                                                                                                                                                                                                                   
  funcionalidades y                                                                                                                                                                                                                                                              
  soporte                                                                                                                                                                                                                                                                        

  Elaboración                                                                                                                                                                                                                                                                     
  informe final                                                                                                                                                                                                                                                                  
  -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

# Presupuesto

[]{#_Toc205131404 .anchor}**Tabla 2**\
Presupuesto proyecto

  ----------------------------------------------------------------------------------
  **Universidad                                                          
  Pontificia                                                             
  Bolivariana -                                                          
  Seccional                                                              
  Bucaramanga**                                                          
  ------------------- ---------------- ----------------- --------------- -----------
  **Título del                         **Mantenimiento                   
  Proyecto de Grado**                  de sistemas de                    
                                       información para                  
                                       la empresa                        
                                       control online                    
                                       S.A.S**                           

                      **Nombre**       **Horas/ Semana** **Horas/Mes**   **Meses**

  Director del        Yurley Estefany  1                 4               6
  Proyecto de Grado   Rueda Romero                                       

  **Duración del      6 meses                                            
  proyecto de grado**                                                    

  **Gastos de                                                            
  personal**                                                             

  Sueldo (Por mes)                     \$ 1.423.500                      

  Sueldo docente (Por                  \$ 214.700                        
  mes)                                                                   

  **Total gastos de**                  **\$ 9.829.200**                  

  **Personal 6                                                           
  meses**                                                                

  **Otros gastos**                                                       

  Transporte (mes)    \$ 120.000                                         

  Almuerzos (mes)     \$ 240.000                                         

  **Total otros       **\$ 2.160.000**                                   
  gastos 6 meses**                                                       

  **Proyecto gasto**                                                     

  **Costo total del   **\$                                               
  proyecto**          11.989.200**                                       
  ----------------------------------------------------------------------------------

# Resultados

## Funciones como desarrollador en la empresa

Durante el desarrollo de las prácticas empresariales en Control Online
International, el rol desempeñado ha sido el de desarrollador junior,
con funciones orientadas al mantenimiento y evolución del software de
gestión documental ControlDoc. Estas funciones están alineadas con el
objetivo general de las prácticas, que busca contribuir a la mejora
continua del sistema mediante tareas técnicas de desarrollo, corrección
y validación funcional.

Las responsabilidades desempeñadas se agrupan en dos ejes fundamentales:

## Soporte y corrección de bugs

Una de las labores más importantes fue la atención a fallos detectados
en el sistema, los cuales pueden originarse en cualquiera de sus capas:
lógica de negocio, presentación o acceso a datos. El proceso comienza
cuando los equipos de soporte o clientes reportan un error a través de
herramientas como Microsoft Planner. Estos reportes son priorizados por
el Product Owner en función del impacto en el sistema, la criticidad del
módulo afectado y la urgencia expresada por el cliente.

Una vez priorizados, los errores son asignados por el Scrum Master al
equipo de desarrollo considerando factores como la disponibilidad, la
experiencia previa en el módulo afectado y los compromisos activos
dentro del Sprint.

Los errores atendidos pueden ir desde fallos visuales menores, como
errores de diseño o presentación, hasta problemas funcionales críticos
que impiden el uso adecuado de módulos esenciales. La función del
desarrollador consiste en:

-   Analizar la causa del error.

-   Reproducir el problema en ambiente controlado.

-   Aplicar medidas correctivas en el código.

-   Validar la solución junto con el equipo de QA.

-   Comunicar el avance y cierre del caso en los Daily Scrum.

## Desarrollo de nuevas funcionalidades

Además del mantenimiento correctivo, el estudiante participó activamente
en el desarrollo de nuevas características del sistema, las cuales
surgen a partir de requerimientos expresados por los usuarios o de
mejoras detectadas por el equipo interno. Estas tareas se documentan en
forma de historias de usuario y se integran al Product Backlog.

La asignación de estos desarrollos considera tanto los objetivos del
Sprint como las habilidades del desarrollador. Sin embargo, como parte
del proceso formativo, también se incentivó la participación del
estudiante en módulos diversos para fortalecer la comprensión integral
del sistema y mejorar las capacidades técnicas.

Los desarrollos pueden abarcar:

-   Incorporación de nuevas vistas o componentes en la interfaz
    (Blazor).

-   Implementación de funcionalidades específicas en backend (C#, .NET).

-   Modificaciones en flujos existentes para adaptarlos a nuevos
    requerimientos.

-   Integración de elementos como captchas, firmas digitales, o
    conectores externos.

Cada funcionalidad desarrollada se valida en múltiples etapas: revisión
interna, pruebas de calidad y pruebas de aceptación por parte del
cliente.

## Aporte al proceso de desarrollo

Además de las funciones técnicas, el estudiante participó activamente en
las dinámicas del marco de trabajo Scrum, como las reuniones de
planificación, las dailys, revisiones y retrospectivas. Esto permitió
una comunicación fluida con el equipo y una visión clara del ciclo de
vida del software.

## Acerca del software ControlDoc y su implementación

El sistema ControlDoc Enterprise, desarrollado por Control Online
International, es una evolución del sistema anterior ControlDoc
Standard. A diferencia de su versión inicial, basada en una arquitectura
monolítica, ControlDoc Enterprise adopta una arquitectura orientada a
microservicios, lo que permite una mayor escalabilidad, mantenimiento
modular y facilidad de despliegue en múltiples entornos.

Esta arquitectura está compuesta por microservicios implementados como
APIs desarrolladas en ASP.NET, organizadas por entidades del dominio.
Dichos servicios se integran mediante un API Gateway, que centraliza las
rutas y controla el acceso a los mismos. El frontend del sistema,
desarrollado en Blazor, consume los servicios del API Gateway y permite
la interacción con los diferentes módulos del sistema.

[]{#_Toc205131367 .anchor}**Figura 5**\
Diagrama de la arquitectura de microservicios de Control Doc

![](media/image8.jpeg){width="6.090194663167104in"
height="2.9722222222222223in"}

*Nota.* Adaptada de Control Online International S.A.S.

## Modalidades de implementación

ControlDoc Enterprise puede ser desplegado tanto en entornos On-Premises
como en la nube con Azure, dependiendo de las necesidades técnicas y
operativas del cliente. A continuación, se describe brevemente cada
modalidad:

## Despliegue On-Premises

Este tipo de implementación utiliza un clúster de Kubernetes sobre
servidores Linux locales, empleando una distribución ligera llamada KS3.
La arquitectura requiere:

-   Un balanceador de carga y un firewall para administrar el tráfico
    externo.

-   Un conjunto de componentes de aplicación dentro del clúster, como
    microservicios, el frontend, base de datos MongoDB para logs y
    RabbitMQ para colas de notificación por correo.

-   Un API Gateway (implementado con Ocelot) y un servicio Ingress para
    enrutar el tráfico hacia los microservicios.

-   Componentes externos como una base de datos SQL Server, un
    Directorio Activo y un servicio NFS para almacenamiento de archivos.

Además, se cuenta con herramientas de DevOps y operación, entre ellas:

-   Azure DevOps Agent, para ejecución de pipelines de integración y
    despliegue continuo.

-   Portainer, para administración gráfica del entorno Kubernetes.

-   SonarQube, para análisis de calidad del código fuente.

[]{#_Toc205131368 .anchor}**Figura 6**\
Diagrama de despliegue On premises de Control Doc

![Interfaz de usuario gráfica, Gráfico El contenido generado por IA
puede ser incorrecto.](media/image9.png){width="5.392702318460192in"
height="4.129794400699913in"}

*Nota.* Adaptada de Control Online International S.A.S.

## Despliegue en la nube (Azure Cloud)

En la modalidad Cloud, el sistema se despliega sobre una Landing Zone de
Azure, e incluye:

-   FrontDoor, que expone una IP pública segura con certificado TLS,
    redireccionando el tráfico hacia los componentes frontend y backend.

-   El frontend, alojado en un servicio de Static Web App.

-   Los microservicios del backend desplegados sobre Azure Kubernetes
    Service (AKS).

-   Un Ingress Controller para exponer los endpoints del API Gateway.

-   Persistencia mediante Azure Files y mensajería asincrónica con
    RabbitMQ.

-   Seguridad y configuración mediante Azure Key Vault, almacenamiento
    de imágenes en Azure Container Registry y monitoreo con Azure
    Monitor y Azure Logging.

[]{#_Toc205131369 .anchor}**Figura 7**\
Diagrama de despliegue Cloud Azure de Control Doc

![Diagrama El contenido generado por IA puede ser
incorrecto.](media/image10.png){width="5.290277777777778in"
height="4.4178029308836395in"}

*Nota.* Adaptada de Control Online International S.A.S.

## Actividades realizadas

Durante el proceso de prácticas empresariales en la empresa Control
Online International, el estudiante llevó a cabo diversas tareas
alineadas con los objetivos del proyecto, abarcando actividades de
formación inicial, corrección de errores, mantenimiento y desarrollo de
nuevas funcionalidades en el sistema de gestión documental ControlDoc.
Estas actividades se ejecutaron bajo el marco de trabajo ágil Scrum y
con la orientación del equipo de desarrollo.

## Fase de capacitación

Previo a la asignación de tareas en producción, el estudiante realizó
una etapa de inducción técnica que incluyó los siguientes procesos:

### Curso de Domain-Driven Design (DDD) en ASP.NET

Formación en la arquitectura utilizada en ControlDoc, abordando la
organización por capas, entidades, repositorios y lógica de negocio
desacoplada de la infraestructura.

### Curso de Blazor

Capacitación en el framework de interfaz de usuario utilizado en
ControlDoc, con énfasis en componentes, consumo de servicios y
separación de lógica con HTML y CSS.

### Proyecto práctico: API y Frontend en Blazor

Al finalizar el Sprint, se realiza una revisión general (Sprint Review)
donde se presentan los avances alcanzados. Posteriormente, las
funcionalidades aprobadas son liberadas en el entorno productivo.
Finalmente, en la reunión de retrospectiva, se identifican oportunidades
de mejora en el proceso.

## Actividades funcionales desarrolladas

A continuación, se presentan las tareas más representativas que el
estudiante desarrolló durante el periodo completo de prácticas
empresariales, en el marco de su participación en el entorno productivo
del sistema ControlDoc. Estas actividades abarcan tanto el desarrollo de
nuevas funcionalidades como la corrección de errores identificados
durante el uso del sistema.

### Inclusión de gestor y visualización de manuales

El estudiante desarrolló un módulo que permite la gestión de manuales en
formato PDF. Este componente ofrece una interfaz para subir manuales
asociados a una descripción y nombre identificador. Los documentos
cargados quedan disponibles para su consulta dentro del sistema,
garantizando su accesibilidad desde el entorno de ControlDoc.

[]{#_Toc205131370 .anchor}**Figura 8**\
Gestor de manuales

![Interfaz de usuario gráfica, Texto, Aplicación El contenido generado
por IA puede ser
incorrecto.](media/image11.png){width="4.584862204724409in"
height="2.140588363954506in"}

*Nota.* Adaptada de Control Online International S.A.S.

[]{#_Toc205131371 .anchor}**Figura 9**\
Apartado de visualización de manuales

![Interfaz de usuario gráfica, Aplicación El contenido generado por IA
puede ser incorrecto.](media/image12.png){width="4.95054571303587in"
height="1.9592257217847768in"}

*Nota.* Adaptada de Control Online International S.A.S.

### Inclusión de gestor y visualización de tutoriales

Complementario al módulo de manuales, el estudiante implementó un
sistema de gestión de tutoriales en video. Los usuarios pueden registrar
enlaces a videos alojados en YouTube, junto con sus respectivos títulos.
Además, el estudiante desarrolló una vista interna donde los clientes
pueden acceder y visualizar estos videos directamente desde el entorno
del sistema, facilitando la capacitación y el soporte.

[]{#_Toc205131372 .anchor}**Figura 10**\
Gestor de tutoriales

![Interfaz de usuario gráfica, Texto, Aplicación El contenido generado
por IA puede ser
incorrecto.](media/image13.png){width="4.803647200349956in"
height="2.257942913385827in"}

*Nota.* Adaptada de Control Online International S.A.S.

[]{#_Toc205131373 .anchor}**Figura 11**\
Apartado de visualización de tutoriales

![Interfaz de usuario gráfica, Aplicación El contenido generado por IA
puede ser incorrecto.](media/image14.png){width="4.725273403324584in"
height="2.3914534120734907in"}

*Nota.* Adaptada de Control Online International S.A.S.

### Ampliación y suspensión de términos de vencimiento

El estudiante construyó dos funcionalidades clave relacionadas con la
gestión del tiempo de los documentos:

-   Ampliación de términos: permite modificar la fecha de vencimiento de
    un documento, generando automáticamente una notificación por correo
    al cliente involucrado.

-   Suspensión temporal: habilita la pausa de los términos de
    vencimiento de un documento, con posibilidad de reanudación
    posterior. Esta acción también desencadena notificaciones
    automáticas vía correo electrónico.

[]{#_Toc205131374 .anchor}**Figura 12**\
Modal de ampliación de términos

![Interfaz de usuario gráfica, Texto, Aplicación, Chat o mensaje de
texto El contenido generado por IA puede ser
incorrecto.](media/image15.png){width="5.164997812773404in"
height="2.315054680664917in"}

*Nota.* Adaptada de Control Online International S.A.S.

[]{#_Toc205131375 .anchor}**Figura 13**\
Modal de suspensión de términos

![Interfaz de usuario gráfica, Texto, Aplicación, Chat o mensaje de
texto El contenido generado por IA puede ser
incorrecto.](media/image16.png){width="5.25248687664042in"
height="2.40668416447944in"}

*Nota.* Adaptada de Control Online International S.A.S.

### Historial de suspensión de documentos:

El estudiante implementó un modal en el frontend que permite consultar
el historial completo de suspensiones realizadas sobre un documento.
Paralelamente, desarrolló en el backend la lógica para registrar y
validar estos eventos cada vez que se genera una nueva suspensión.

[]{#_Toc205131376 .anchor}**Figura 14**\
Historial de suspensión

![Interfaz de usuario gráfica, Aplicación El contenido generado por IA
puede ser incorrecto.](media/image17.png){width="6.320833333333334in"
height="2.437405949256343in"}

*Nota.* Adaptada de Control Online International S.A.S.

### Historial de ampliación de términos:

Se desarrolló un componente que registra los cambios realizados en las
fechas de vencimiento de los documentos, permitiendo visualizar tanto la
fecha original como la nueva asignada. Esta funcionalidad facilita la
trazabilidad de modificaciones realizadas en los términos documentales
por parte de los usuarios.

[]{#_Toc205131377 .anchor}**Figura 15**\
Historial de ampliación

![Interfaz de usuario gráfica, Texto, Aplicación, Correo electrónico El
contenido generado por IA puede ser
incorrecto.](media/image18.png){width="5.575694444444444in"
height="1.955294181977253in"}

*Nota.* Adaptada de Control Online International S.A.S.

### Lógica condicional en el selector de fechas (ampliación de términos):

En el modal de ampliación, el estudiante desarrolló una validación que
consulta un endpoint para determinar si la entidad es pública o privada.
Con base en esta verificación, el selector de fechas se habilita o
bloquea dinámicamente, garantizando el cumplimiento de las reglas
definidas por tipo de entidad.

[]{#_Toc205131378 .anchor}**Figura 16**\
Historial de ampliación con lógica adicional

![Interfaz de usuario gráfica, Texto, Aplicación El contenido generado
por IA puede ser
incorrecto.](media/image19.png){width="4.947916666666667in"
height="2.523109142607174in"}

*Nota.* Adaptada de Control Online International S.A.S.

### Dinámica de etiquetas en el módulo PQRSd:

En este módulo, el estudiante integró una lógica condicional que permite
modificar los labels de los formularios según el tipo de entidad que
accede al sistema. Esta funcionalidad consulta una llave de
configuración que determina los textos personalizados a mostrar,
mejorando así la experiencia de usuario en entornos multiempresa.

[]{#_Toc205131379 .anchor}**Figura 17**\
Vista PQR con llave activa

![Interfaz de usuario gráfica, Aplicación El contenido generado por IA
puede ser incorrecto.](media/image20.png){width="4.973206474190726in"
height="2.0106058617672793in"}

*Nota.* Adaptada de Control Online International S.A.S.

[]{#_Toc205131380 .anchor}**Figura 18**\
Vista PQR sin llave activada

![Interfaz de usuario gráfica, Aplicación El contenido generado por IA
puede ser incorrecto.](media/image21.png){width="5.213962160979878in"
height="2.4747626859142606in"}

*Nota.* Adaptada de Control Online International S.A.S.

### Componente de carga para descarga de imágenes en expedientes

En el módulo de expedientes, se integró un banner de descarga con
indicador de carga. Esto se implementó debido a que algunas imágenes,
por su tamaño, tardaban en descargarse. El desarrollo permitió una mejor
experiencia de usuario mientras se espera la visualización del
expediente.

[]{#_Toc205131381 .anchor}**Figura 19**\
Spiner Loader de descarga de imagen.

![Interfaz de usuario gráfica, Texto, Aplicación El contenido generado
por IA puede ser
incorrecto.](media/image22.png){width="5.340653980752406in"
height="2.6190748031496063in"}

*Nota.* Adaptada de Control Online International S.A.S.

### Botón para limpiar indexación de expedientes

Se implementó una opción que permite limpiar la información de
indexación durante el proceso de foliado, en caso de que el usuario
cometa un error. Esta herramienta mejora la precisión de los datos
registrados y evita la necesidad de reiniciar el proceso completo.

[]{#_Toc205131382 .anchor}**Figura 20**\
Diagrama de despliegue Cloud Azure de Control Doc

![Interfaz de usuario gráfica, Texto, Aplicación, Chat o mensaje de
texto El contenido generado por IA puede ser
incorrecto.](media/image23.png){width="4.938254593175853in"
height="2.7762193788276464in"}

*Nota.* Adaptada de Control Online International S.A.S.

## Corrección de errores (Bugs)

### Ajuste en el manejo de errores al tramitar documentos:

En el módulo Document Management, el estudiante identificó que el método
encargado de tramitar documentos no devolvía mensajes de error adecuados
al frontend. Modificó el response para mostrar mensajes más específicos
y útiles al usuario según la causa del fallo.

### Duplicación de tarjetas al desanular documentos:

El estudiante detectó que al desanular un documento, este seguía
apareciendo en la tarjeta de anulados. Ajustó la lógica del backend para
que desactive el registro anterior al momento de desanular, evitando la
duplicación en las vistas del sistema.

### No actualiza las tarjetas tras aplicar filtros (bandeja de digitalización):

Al aplicar filtros en la bandeja de digitalización, los contadores de
las tarjetas no se actualizaban correctamente. El estudiante modificó el
método de filtrado en el frontend para sincronizar la información visual
con los resultados de la búsqueda.

### Error en la visualización de copias en la bandeja de tareas documentales:

El estudiante detectó que al abrir una tarea en la bandeja de tareas
documentales y acceder al modal de copias, el sistema no ejecutaba
correctamente la consulta al endpoint correspondiente. Como resultado,
no se cargaban las personas asignadas en copia a dicha tarea. Realizó el
ajuste necesario para asegurar que, al abrir el modal, se invoque
correctamente el servicio y se visualice la información esperada.

### Exclusión de documentos anulados en la bandeja de tareas:

Se ajustó la consulta del backend responsable de listar los documentos
visibles en la bandeja de tareas, para excluir aquellos que se
encuentran anulados. Esto evitó que los usuarios visualizaran documentos
sin vigencia operativa.

### Validación del límite de folios al cargar imágenes:

Se detectó que el sistema permitía la carga o duplicación de imágenes
con más de 300 folios, superando el límite permitido. Se implementó una
validación en el frontend que bloquea la carga de archivos que excedan
dicho límite.

### Fallo en el botón de eliminación de usuarios del modal de clasificación:

Se corrigió un error en el modal de clasificación, donde al intentar
eliminar un usuario asignado, no se actualizaba correctamente la lista
visible. El estudiante ajustó la lógica para asegurar la limpieza de la
lista tras cada eliminación.

# Conclusiones

Durante el desarrollo completo de las prácticas empresariales en Control
Online International, el estudiante tuvo la oportunidad de participar
activamente en procesos reales de mantenimiento, evolución y soporte del
sistema de gestión documental ControlDoc. A partir de esta experiencia,
se presentan las siguientes conclusiones:

-   **Adaptarse a un entorno empresarial exige una mentalidad de mejora
    continua:** La participación en un entorno productivo consolidado
    permitió al estudiante comprender que el desarrollo de software no
    se limita al aspecto técnico, sino que implica adaptarse a
    estándares de calidad, flujos de trabajo ágiles y cambios frecuentes
    en los requerimientos.

-   **La arquitectura y la metodología son fundamentales para la
    escalabilidad del software:** La experiencia directa con una
    arquitectura basada en Domain Driven Design (DDD) y la aplicación
    del marco de trabajo Scrum permitieron interiorizar principios de
    organización del código, desacoplamiento funcional y priorización de
    tareas alineadas a los objetivos del cliente.

-   **Cada funcionalidad debe tener una justificación centrada en el
    usuario:** El desarrollo de módulos como el gestor de manuales,
    tutoriales, historial de términos y validaciones dinámicas evidenció
    la importancia de construir soluciones que respondan a necesidades
    específicas, mejoren la experiencia del usuario y optimicen la
    operación de las organizaciones.

-   **Las prácticas fortalecen el dominio de herramientas y flujos de
    trabajo profesionales:** El uso de herramientas como Microsoft
    Planner, la interacción constante en dailys y la validación mediante
    procesos QA y UAT, ofrecieron una inmersión real en dinámicas de
    trabajo profesional, fortaleciendo habilidades técnicas y
    comunicativas.

-   **El aprendizaje técnico fue progresivo y constante**: A lo largo de
    la práctica, el estudiante pasó de tareas formativas a desarrollos
    funcionales de impacto, aplicando tecnologías como ASP.NET Core,
    Blazor, GitHub, Docker y Telerik. Esto refleja una curva de
    aprendizaje sólida, fortaleciendo su perfil como futuro ingeniero de
    sistemas.

Finalmente, se concluye que los objetivos planteados al inicio del
proceso fueron alcanzados satisfactoriamente, consolidando competencias
clave tanto en el ámbito técnico como en la comprensión de procesos
reales de desarrollo de software empresarial.

# Referencias

Beynon-Davies, P. (2018). *Sistemas de bases de datos.* Reverté.

Brayitaan. (17 de Mayo de 2024). *Medium*. Obtenido de Metodología
Scrum:
https://medium.com/@brayanzjimenez179/metodologia-scrum-3ef4a77b467e

Campo, G. D. (2009). *Patrones de diseño, refactorización y
antipatrones.*

Chacon, S., & Straub, B. (2014). *Pro Git.* Apress.

Control Online International. (2023). *SGDEA ControlDoc*. Obtenido de
Control Online International:
https://controlonlineinternational.com/software-controldoc/

Docunet Web. (s.f.). *¿Qué es Docunet?* Obtenido de Docunet Web:
https://docunetweb.com.co/que-es-docunet/

Entity Framework. (2021). Entity Framework. *Architecture*, 20.

Evans, E. (2003). *Domain-Driven Design: Tackling Complexity in the
Heart of Software.* Boston, Massachusetts, EE.UU.: Addison-Wesley.

Guia TIC. (s.f.). *SIGED - Software de Gestión Documental y
Correspondencia en la Nube*. Obtenido de Guia TIC:
https://guiatic.com/co/67-software-gestion-documental-administracion-electronica-de-documentos/1392-siged-software-de-gestion-documental-y-correspondencia-en-la-nube-solucion-digital-para-el-control-de-la-correspondencia-y-el

Heiland, L., Hauser, M., & Bogner, J. (2023). Design patterns for
AI-based systems: A multivocal literature review and pattern repository.
*2023 IEEE/ACM 2nd International Conference on AI Engineering--Software
Engineering for AI (CAIN)* (págs. 184--196). IEEE.

Herranz, R., & de Hontanares, M. (2010). El documento electrónico: un
enfoque archivístico. *Revista General de Información y Documentación*,
391--408.

Himschoot, P. (2020). *Microsoft Blazor.* Apress.

Lock, A. (2023). *ASP.NET Core in Action.* Simon and Schuster.

Maleshkova, M., Pedrinaci, C., & Domingue, J. (2010). Investigating web
APIs on the World Wide Web. *2010 Eighth IEEE European Conference on Web
Services* (págs. 107--114). IEEE.

Martin, R. C. (2017). *Clean Architecture: A Craftsman\'s Guide to
Software Structure and Design.* Boston, Estados Unidos: Prentice Hall.

MinCiencias. (s.f.). *Sistema de Gestión de Documento Electrónico de
Archivo*. Obtenido de MinCiencias:
https://minciencias.gov.co/glosario/sistema-gestion-documento-electronico-archivo-sgdea

Prodygytek. (s.f.). *Process, Document and Data Solutions*. Obtenido de
Prodygytek: https://www.prodygytek.com/index.html

Progress Software Corporation. (2024). *Telerik UI for Blazor*. Obtenido
de Telerik: https://www.telerik.com/blazor-ui

QWebDocuments. (s.f.). *Documentación PHVA*. Obtenido de QWebDocuments:
https://qwebcloud.com/productos/qwebdocuments/

# 
