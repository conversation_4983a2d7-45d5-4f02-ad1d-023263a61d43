package se.firme.ms.datos.models.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.ProcesoFirma;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-23T18:28:34")
@StaticMetamodel(UsuarioFirmaArchivoOtp.class)
public class UsuarioFirmaArchivoOtp_ { 

    public static volatile SingularAttribute<UsuarioFirmaArchivoOtp, Integer> estado;
    public static volatile SingularAttribute<UsuarioFirmaArchivoOtp, ProcesoFirma> idProcesoFirma;
    public static volatile SingularAttribute<UsuarioFirmaArchivoOtp, Long> idUsuarioOtpFirmaArchivo;
    public static volatile SingularAttribute<UsuarioFirmaArchivoOtp, ArchivoFirma> idArchivoFirma;

}