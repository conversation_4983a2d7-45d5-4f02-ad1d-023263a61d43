package se.firme.ms.datos.models.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value="EclipseLink-2.5.2.v20140319-rNA", date="2025-09-19T14:55:24")
@StaticMetamodel(Sku.class)
public class Sku_ { 

    public static volatile SingularAttribute<Sku, String> descripcion;
    public static volatile SingularAttribute<Sku, Boolean> estado;
    public static volatile SingularAttribute<Sku, String> tipoServicio;
    public static volatile SingularAttribute<Sku, Integer> cantidadOtrosFirmantes;
    public static volatile SingularAttribute<Sku, Integer> idSku;
    public static volatile SingularAttribute<Sku, Date> fechaRegistro;
    public static volatile SingularAttribute<Sku, Integer> vigenciaMeses;
    public static volatile SingularAttribute<Sku, String> tipoValidacion;
    public static volatile SingularAttribute<Sku, Integer> cantidadFirmas;

}