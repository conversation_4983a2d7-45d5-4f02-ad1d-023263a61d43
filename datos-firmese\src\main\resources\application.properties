# To change this license header, choose License Headers in Project Properties.
# To change this template file, choose Too<PERSON> | Templates
# and open the template in the editor.

# Datasource configuration

spring.datasource.url=**************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=none

#logging.level.org.hibernate.SQL=debug

# Configuración de rutas para desarrollo local
routes.custom.file=C:/Users/<USER>/firmese/file

# Configuración de logging para desarrollo
logging.level.se.firme.ms.models.service.ProcesoFirmaServiceImpl=INFO
logging.level.se.firme.commons.firmese.service.EmailService=DEBUG

# Configuración de email para desarrollo (opcional - para evitar envío real)
# spring.mail.host=localhost
# spring.mail.port=1025
# spring.mail.username=
# spring.mail.password=