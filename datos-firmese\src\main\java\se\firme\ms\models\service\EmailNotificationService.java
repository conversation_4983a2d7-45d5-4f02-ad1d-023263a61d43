package se.firme.ms.models.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import se.firme.commons.firmese.service.EmailService;
import se.firme.commons.firmese.service.EmailTemplateService;
import se.firme.ms.models.service.helper.EmailDataHelper;
import se.firme.commons.firmese.dto.DatosEmailMultipleFirmaDTO;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

@Service
public class EmailNotificationService {
    
    private static Logger logger = Logger.getLogger(EmailNotificationService.class.getName());
    
    @Autowired
    private Environment env;

    @Autowired
    private EmailDataHelper emailDataHelper; // Inyecta el bean del Helper para obtener datos para el correo

    /**
     * Envía notificación agrupada con múltiples documentos
     */
    public boolean enviarNotificacionAgrupada(String token, String email, List<String> nombresArchivos, List<String> rutasCompletas, DatosEmailMultipleFirmaDTO datosEmail) {
        try {
            // Validaciones de entrada
            if (token == null || token.trim().isEmpty()) {
                logger.warning("Token null o vacío para envío agrupado a: " + email);
                return false;
            }
            
            if (email == null || email.trim().isEmpty()) {
                logger.warning("Email null o vacío para envío agrupado");
                return false;
            }
            
            logger.info("Enviando notificación agrupada a: " + email);
            
            if (nombresArchivos != null && !nombresArchivos.isEmpty()) {
                logger.info("Documentos a incluir: " + String.join(", ", nombresArchivos));
            }
            
            String baseUrl = getBaseUrl();
            if (baseUrl == null || baseUrl.trim().isEmpty()) {
                logger.warning("Base URL null o vacía, usando valor por defecto");
                baseUrl = "http://localhost:8080"; // Fallback
            }
            
            // Preparar adjuntos múltiples
            List<String[]> adjuntos = new ArrayList<>();
            
            if (rutasCompletas != null && nombresArchivos != null) {
                int maxFiles = Math.min(rutasCompletas.size(), nombresArchivos.size());
                
                for (int i = 0; i < maxFiles; i++) {
                    String rutaCompleta = rutasCompletas.get(i);
                    if (rutaCompleta != null && !rutaCompleta.trim().isEmpty()) {
                        File archivo = new File(rutaCompleta);
                        if (archivo.exists() && archivo.isFile()) {
                            String[] infoArchivo = new String[2];
                            infoArchivo[0] = archivo.getParent() + "/"; // Directorio
                            infoArchivo[1] = archivo.getName();         // Nombre archivo
                            adjuntos.add(infoArchivo);
                            
                            logger.info("Adjunto preparado: " + archivo.getName());
                        } else {
                            logger.warning("Archivo no encontrado para adjuntar: " + rutaCompleta);
                        }
                    }
                }
            }
            
            // Enviar correo con template para múltiples documentos
            try {
                // generar asunto para el correo
                String asuntoPersonalizado = EmailTemplateService.getAsuntoFirmaPersonalizado(datosEmail);
                if (adjuntos.isEmpty()) {
                    // Enviar sin adjuntos
                    EmailService.enviarCorreo(email, asuntoPersonalizado, 
                                            EmailTemplateService.getTemplateFirmaMultiple(token, baseUrl, datosEmail), null);
                    logger.info("Correo agrupado sin adjuntos enviado a: " + email);
                } else {
                    // Enviar con adjuntos
                    EmailService.enviarCorreo(email, asuntoPersonalizado, 
                                            EmailTemplateService.getTemplateFirmaMultiple(token, baseUrl, datosEmail), adjuntos);
                    logger.info("Correo agrupado enviado exitosamente a: " + email + " con " + adjuntos.size() + " adjunto(s)");
                }
                return true;
            } catch (Exception emailEx) {
                logger.severe("Error en EmailService para " + email + ": " + emailEx.getMessage());
                throw emailEx;
            }
            
        } catch (Exception e) {
            logger.severe("Error enviando notificación agrupada a " + (email != null ? email : "email_null") + ": " + e.getMessage());
            e.printStackTrace();
            
            // Fallback: enviar sin adjuntos
            try {
                logger.info("Reintentando envío agrupado sin adjuntos a: " + email);
                // sacar asunto personalizado
                String asuntoPersonalizado = EmailTemplateService.getAsuntoFirmaPersonalizado(datosEmail);
                String baseUrl = getBaseUrl();
                if (baseUrl == null) baseUrl = "http://localhost:8080";
                
                EmailService.enviarCorreo(email, asuntoPersonalizado, 
                                        EmailTemplateService.getTemplateFirmaMultiple(token, baseUrl, datosEmail), null);
                logger.info("Correo agrupado sin adjuntos enviado a: " + email);
                return true;
            } catch (Exception fallbackEx) {
                logger.severe("Error en fallback para notificación agrupada a " + email + ": " + fallbackEx.getMessage());
                return false;
            }
        }
    }

    /**
     * Envía notificación simple con un solo documento
     */
    public boolean enviarNotificacionSolicitud(String token, String email, String medio, String rutaCompleta, String nombreArchivo) {
        try {
            logEnvironmentInfo();
            
            // Obtener URL base
            String baseUrl = getBaseUrl();
            
            // Preparar adjuntos si el archivo existe
            List<String[]> adjuntos = null;
            // datos para el correo
            
            if (rutaCompleta != null && new File(rutaCompleta).exists()) {
                adjuntos = new ArrayList<>();
                String[] infoArchivo = new String[2];
                
                // Separar correctamente directorio y nombre del archivo
                File archivo = new File(rutaCompleta);
                String directorio = archivo.getParent() + "/"; // Directorio con separador final
                String nombreArchivoSolo = archivo.getName();   // Solo el nombre del archivo
                
                infoArchivo[0] = directorio;        // Solo el directorio con /
                infoArchivo[1] = nombreArchivoSolo; // Solo el nombre del archivo
                adjuntos.add(infoArchivo);
                
                logger.info("Archivo adjunto preparado - Directorio: " + directorio + " - Archivo: " + nombreArchivoSolo);
                logger.info("Ruta completa verificada: " + rutaCompleta);
            } else {
                logger.warning("Archivo no encontrado para adjuntar: " + rutaCompleta);
            }

            
            logger.info("Enviando correo a: " + email + " con token: " + token.substring(0, Math.min(10, token.length())) + "...");
            logger.info("URL base utilizada: " + baseUrl);
            DatosEmailMultipleFirmaDTO datosEmail = emailDataHelper.extraerDatosNotificacionCorreoSimple(email, nombreArchivo, token);
            String asuntoPersonalizado = EmailTemplateService.getAsuntoFirmaPersonalizado(datosEmail);
            switch (medio) {
                case "eml":
                    EmailService.enviarCorreo(email, asuntoPersonalizado, EmailTemplateService
                            .getTemplateFirmaMultiple(token, baseUrl, datosEmail), adjuntos);
                    break;
                    
                default:
                    EmailService.enviarCorreo(email, asuntoPersonalizado, EmailTemplateService
                            .getTemplateFirmaMultiple(token, baseUrl, datosEmail), adjuntos);
                    break;
            }
            
            logger.info("Correo enviado exitosamente a: " + email);
            return true;
            
        } catch (Exception e) {
            logger.severe("Error enviando notificación a " + email + ": " + e.getMessage());
            
            // Si falla con adjuntos, intentar sin adjuntos como fallback
            if (rutaCompleta != null) {
                try {
                    logger.info("Reintentando envío sin adjuntos a: " + email);
                    return enviarNotificacionSolicitudSinAdjuntos(token, email, medio);
                } catch (Exception fallbackEx) {
                    logger.severe("Error en fallback para " + email + ": " + fallbackEx.getMessage());
                }
            }
            return false;
        }
    }

    /**
     * Envío sin adjuntos como fallback
     */
    private boolean enviarNotificacionSolicitudSinAdjuntos(String token, String email, String medio) {
        try {
            String baseUrl = getBaseUrl();
            DatosEmailMultipleFirmaDTO datosEmail = emailDataHelper.extraerDatosNotificacionCorreoSimple(email, null, token);
            String asuntoPersonalizado = EmailTemplateService.getAsuntoFirmaPersonalizado(datosEmail);
            
            logger.info("Enviando correo sin adjuntos a: " + email + " con URL: " + baseUrl);
            
            switch (medio) {
                case "eml":
                    EmailService.enviarCorreo(email, asuntoPersonalizado, EmailTemplateService
                            .getTemplateFirmaMultiple(token, baseUrl, datosEmail), null);
                    break;
                    
                default:
                    EmailService.enviarCorreo(email, asuntoPersonalizado, EmailTemplateService
                            .getTemplateFirmaMultiple(token, baseUrl,datosEmail), null);
                    break;
            }
            
            logger.info("Correo sin adjuntos enviado exitosamente a: " + email);
            return true;
            
        } catch (Exception e) {
            logger.severe("Error enviando notificación sin adjuntos a " + email + ": " + e.getMessage());
            return false;
        }
    }

    private String getBaseUrl() {
        // Intentar obtener el perfil activo
        String activeProfile = env.getProperty("spring.profiles.active", "default");
        
        logger.info("Perfil activo detectado: " + activeProfile);
        
        // Intentar obtener URL específica del perfil
        String baseUrl = null;
        
        switch (activeProfile.toLowerCase()) {
            case "prod":
            case "production":
                baseUrl = env.getProperty("app.base.url.prod", "https://www.firme.se:3191");
                break;
            case "test":
            case "testing":
                baseUrl = env.getProperty("app.base.url.test", "https://test.firme.se:3191");
                break;
            case "dev":
            case "development":
            case "local":
            default:
                baseUrl = env.getProperty("app.base.url.dev", "http://localhost:3000");
                break;
        }
        
        // Fallback a la propiedad general si no encuentra específica
        if (baseUrl == null) {
            baseUrl = env.getProperty("FIRMESE_BASE_URI_FRONTEND");
        }
        
        // Fallback final si ninguna está configurada
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            baseUrl = "http://localhost:8080"; // Valor por defecto
        }
        
        logger.info("URL base obtenida: " + baseUrl);
        return baseUrl;
    }

    private void logEnvironmentInfo() {
        logger.info("=== INFORMACIÓN DEL AMBIENTE ===");
        logger.info("Perfil activo: " + env.getProperty("spring.profiles.active"));
        logger.info("FIRMESE_BASE_URI_FRONTEND: " + env.getProperty("FIRMESE_BASE_URI_FRONTEND"));
        logger.info("app.base.url.dev: " + env.getProperty("app.base.url.dev"));
        logger.info("app.base.url.test: " + env.getProperty("app.base.url.test"));
        logger.info("app.base.url.prod: " + env.getProperty("app.base.url.prod"));
        logger.info("URL base calculada: " + getBaseUrl());
        logger.info("================================");
    }
}