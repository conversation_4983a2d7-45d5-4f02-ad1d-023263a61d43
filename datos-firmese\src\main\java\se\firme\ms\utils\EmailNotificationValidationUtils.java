package se.firme.ms.utils;

import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.datos.models.entity.Token;

/**
 * Util para validaciones de extracción de datos para notificaciones de firma al correo.
 *
 * Centraliza las validaciones en el proceso de extracción de datos para la notificación de firma en el correo
 *
 * <AUTHOR>
 */
public final class EmailNotificationValidationUtils {

    
    /**
     * Valida que el ID de usuario no sea null o inválido.
     *
     * @param idUsuario ID del usuario a validar
     * @throws FirmaException Si el ID es inválido
     */
    public static void validarIdUsuario(Long idUsuario) throws FirmaException {
        if (idUsuario == null || idUsuario <= 0) {
            throw new FirmaException("El id del usuario no puede ser null");
        }
    }

    /**
     * Valida que el email del firmante no sea null o vacío.
     *
     * @param emailFirmante Email del firmante a validar
     * @throws FirmaException Si el email es inválido
     */
    public static void validarEmailFirmante(String emailFirmante) throws FirmaException {
        if (emailFirmante == null || emailFirmante.trim().isEmpty()) {
            throw new FirmaException("Email del firmante es requerido para procesar la extracción de datos");
        }
    }

    /**
     * Valida que el token string no sea null o vacío.
     *
     * @param tokenString Token en formato string a validar
     * @throws FirmaException Si el token es inválido
     */
    public static void validarTokenString(String tokenString) throws FirmaException {
        if (tokenString == null || tokenString.trim().isEmpty()) {
            throw new FirmaException("Token es requerido para extraer datos de la solicitud");
        }
    }

    /**
     * Valida que los IDs de documentos no sean null o vacíos.
     *
     * @param idsDocumentos String con IDs de documentos
     * @throws FirmaException Si los IDs son inválidos
     */
    public static void validarIdsDocumentos(String idsDocumentos) throws FirmaException {
        if (idsDocumentos == null || idsDocumentos.trim().isEmpty()) {
            throw new FirmaException("IDs de documentos son requeridos para obtener nombres de archivos para la notificación de firma");
        }
    }
    
    /**
     * Valida que un usuario remitente tenga los datos completos necesarios.
     *
     * @param usuarioRemitente Usuario remitente a validar
     * @param idUsuario ID del usuario para contexto en mensajes de error
     * @throws FirmaException Si el usuario es null o le faltan datos obligatorios
     */
    public static void validarUsuarioRemitente(Usuario usuarioRemitente, Long idUsuario) throws FirmaException {
        if (usuarioRemitente == null) {
            throw new FirmaException("Usuario remitente no encontrado con ID: " + idUsuario);
        }

        String nombreCompleto = usuarioRemitente.getNombreCompleto();
        if (nombreCompleto == null || nombreCompleto.trim().isEmpty()) {
            throw new FirmaException("Usuario remitente sin nombre completo registrado - ID: " + idUsuario);
        }

        String emailRemitente = usuarioRemitente.getCorreoElectronico();
        if (emailRemitente == null || emailRemitente.trim().isEmpty()) {
            throw new FirmaException("Usuario remitente sin correo electrónico registrado - ID: " + idUsuario);
        }
    }

    /**
     * Valida que un usuario firmante tenga los datos necesarios.
     *
     * @param usuarioFirmante Usuario firmante a validar
     * @param emailFirmante Email del firmante para contexto en errores
     * @throws FirmaException Si el usuario es null o le faltan datos obligatorios
     */
    public static void validarUsuarioFirmante(Usuario usuarioFirmante, String emailFirmante) throws FirmaException {
        if (usuarioFirmante == null) {
            throw new FirmaException("Usuario firmante no encontrado para email: " + emailFirmante);
        }

        String nombreCompleto = usuarioFirmante.getNombreCompleto();
        if (nombreCompleto == null || nombreCompleto.trim().isEmpty()) {
            throw new FirmaException("Usuario firmante sin nombre completo registrado - Email: " + emailFirmante);
        }
    }

    /**
     * Valida que un token tenga los datos básicos necesarios.
     *
     * @param token Token a validar
     * @throws FirmaException Si el token es null o inválido
     */
    public static void validarToken(Token token) throws FirmaException {
        if (token == null) {
            throw new FirmaException("Token es requerido para extraer datos para la notificación de firma");
        }

        if (token.getIdUsuario() == null) {
            throw new FirmaException("Token sin usuario remitente asociado - ID Token: " + token.getIdToken());
        }
    }

    /**
     * Valida que un usuario tenga datos completos (nombre y email).
     *
     * @param usuario Usuario a validar
     * @param contexto Contexto para el mensaje de error 
     * @throws FirmaException Si el usuario es null o le faltan datos
     */
    public static void validarUsuarioCompleto(Usuario usuario, String contexto) throws FirmaException {
        if (usuario == null) {
            throw new FirmaException(contexto + " no encontrado");
        }

        String nombreCompleto = usuario.getNombreCompleto();
        if (nombreCompleto == null || nombreCompleto.trim().isEmpty()) {
            throw new FirmaException(contexto + " sin nombre completo registrado");
        }

        String email = usuario.getCorreoElectronico();
        if (email == null || email.trim().isEmpty()) {
            throw new FirmaException(contexto + " sin correo electrónico registrado");
        }
    }
}
